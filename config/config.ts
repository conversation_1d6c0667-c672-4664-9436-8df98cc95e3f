import { TIME, ECONOMY, POPULATION, PRODUCTION } from "../constants/gameConstants"

export const CONFIG = {
  // Game Loop Settings
  GAME: {
    TICK_INTERVAL_MS: TIME.DEFAULT_TICK_INTERVAL_MS,
    AUTO_SAVE_INTERVAL_TICKS: TIME.AUTO_SAVE_INTERVAL_TICKS,
  },

  // Production Chains and Industries - keep existing recipes but use constants for values
  PRODUCTION_CHAINS: {
    // Keep existing RECIPES object as is - it's complex data, not simple constants
    RECIPES: {
      // Food Processing
      rum_distillery: {
        id: "rum_distillery",
        name: "Rum Distillery",
        category: "food_processing",
        inputs: { sugar: 2 }, // 2 sugar per tick
        outputs: { rum: 1 }, // 1 rum per tick
        baseEfficiency: 0.85, // 85% efficiency
        processingTime: 1, // Instant processing
        workerRequirement: 50, // Need 50 workers
        upkeepCost: 10, // Gold per tick
        buildCost: 2500, // Cost for player to build
        buildTime: 5, // 5 ticks to build
        buildMaterials: { wood: 20, tools: 5 }, // Required materials
        description: "Converts sugar into premium rum",
      },

      flour_mill: {
        id: "flour_mill",
        name: "Flour Mill",
        category: "food_processing",
        inputs: { wheat: 3 },
        outputs: { flour: 2 },
        baseEfficiency: 0.9,
        processingTime: 1,
        workerRequirement: 30,
        upkeepCost: 5,
        buildCost: 1800,
        buildTime: 4,
        buildMaterials: { wood: 15, tools: 3 },
        description: "Grinds wheat into flour for baking",
      },

      bakery: {
        id: "bakery",
        name: "Bakery",
        category: "food_processing",
        inputs: { flour: 2 },
        outputs: { bread: 3 },
        baseEfficiency: 0.8,
        processingTime: 1,
        workerRequirement: 25,
        upkeepCost: 8,
        buildCost: 1500,
        buildTime: 3,
        buildMaterials: { wood: 10, tools: 2 },
        description: "Bakes flour into fresh bread",
      },

      // Manufacturing
      forge: {
        id: "forge",
        name: "Forge",
        category: "manufacturing",
        inputs: { wood: 1, iron: 2 },
        outputs: { tools: 1 },
        baseEfficiency: 0.75,
        processingTime: 2, // Takes 2 ticks
        workerRequirement: 40,
        upkeepCost: 15,
        buildCost: 3500,
        buildTime: 6,
        buildMaterials: { wood: 25, iron: 15 },
        description: "Combines wood and iron to create tools",
      },

      armory: {
        id: "armory",
        name: "Armory",
        category: "manufacturing",
        inputs: { iron: 3 },
        outputs: { weapons: 1 },
        baseEfficiency: 0.7,
        processingTime: 2,
        workerRequirement: 35,
        upkeepCost: 12,
        buildCost: 4000,
        buildTime: 7,
        buildMaterials: { wood: 20, iron: 20, tools: 5 },
        description: "Forges iron into weapons and armor",
      },

      textile_mill: {
        id: "textile_mill",
        name: "Textile Mill",
        category: "manufacturing",
        inputs: { cloth: 2 },
        outputs: { fine_cloth: 1 },
        baseEfficiency: 0.8,
        processingTime: 1,
        workerRequirement: 45,
        upkeepCost: 18,
        buildCost: 3200,
        buildTime: 5,
        buildMaterials: { wood: 30, tools: 8 },
        description: "Processes basic cloth into fine textiles",
      },

      // Advanced Manufacturing
      shipyard: {
        id: "shipyard",
        name: "Shipyard",
        category: "advanced_manufacturing",
        inputs: { wood: 10, iron: 5, tools: 3 },
        outputs: { ship_parts: 1 },
        baseEfficiency: 0.6,
        processingTime: 5, // Takes 5 ticks
        workerRequirement: 100,
        upkeepCost: 50,
        buildCost: 8000,
        buildTime: 12,
        buildMaterials: { wood: 100, iron: 50, tools: 20 },
        description: "Builds ship components from raw materials",
      },
    },

    // Use centralized constants for categories
    INDUSTRY_CATEGORIES: {
      primary: {
        name: "Primary Industries",
        description: "Extract raw materials",
        populationRequirement: PRODUCTION.REQUIREMENTS.PRIMARY.POPULATION,
        wealthRequirement: PRODUCTION.REQUIREMENTS.PRIMARY.WEALTH,
      },
      food_processing: {
        name: "Food Processing",
        description: "Process raw food into refined products",
        populationRequirement: PRODUCTION.REQUIREMENTS.FOOD_PROCESSING.POPULATION,
        wealthRequirement: PRODUCTION.REQUIREMENTS.FOOD_PROCESSING.WEALTH,
      },
      manufacturing: {
        name: "Manufacturing",
        description: "Create tools and manufactured goods",
        populationRequirement: PRODUCTION.REQUIREMENTS.MANUFACTURING.POPULATION,
        wealthRequirement: PRODUCTION.REQUIREMENTS.MANUFACTURING.WEALTH,
      },
      advanced_manufacturing: {
        name: "Advanced Manufacturing",
        description: "Complex production requiring multiple inputs",
        populationRequirement: PRODUCTION.REQUIREMENTS.ADVANCED_MANUFACTURING.POPULATION,
        wealthRequirement: PRODUCTION.REQUIREMENTS.ADVANCED_MANUFACTURING.WEALTH,
      },
    },

    // Use centralized efficiency modifiers
    EFFICIENCY_MODIFIERS: {
      WEALTH_BONUS: {
        wealthy: PRODUCTION.WEALTHY_CITY_EFFICIENCY_BONUS,
        normal: 1.0,
        poor: PRODUCTION.POOR_CITY_EFFICIENCY_PENALTY,
      },
      POPULATION_BONUS: {
        large: PRODUCTION.LARGE_CITY_EFFICIENCY_BONUS,
        medium: 1.0,
        small: PRODUCTION.SMALL_CITY_EFFICIENCY_PENALTY,
      },
      RANDOM_VARIANCE: PRODUCTION.EFFICIENCY_VARIANCE,
      PLAYER_OWNERSHIP_BONUS: PRODUCTION.PLAYER_OWNERSHIP_BONUS,
    },

    // Use centralized development constants
    INDUSTRY_DEVELOPMENT: {
      DEVELOPMENT_CHANCE: PRODUCTION.INDUSTRY_DEVELOPMENT_CHANCE,
      BUILD_REQUIREMENTS: {
        POPULATION_MULTIPLIER: 1.0,
        WEALTH_MULTIPLIER: 1.0,
        INPUT_AVAILABILITY: 0.7,
      },
      CONSTRUCTION_TIME: {
        food_processing: PRODUCTION.CONSTRUCTION_TIME.FOOD_PROCESSING,
        manufacturing: PRODUCTION.CONSTRUCTION_TIME.MANUFACTURING,
        advanced_manufacturing: PRODUCTION.CONSTRUCTION_TIME.ADVANCED_MANUFACTURING,
      },
    },
  },

  // Port Upgrades
  PORT_UPGRADES: {
    AVAILABLE_UPGRADES: {
      basic_dock_expansion: {
        id: "basic_dock_expansion",
        name: "Dock Expansion",
        description: "Expand the harbor with additional docking space for faster loading",
        cost: 5000,
        buildTime: 8,
        effects: {
          loadingSpeedMultiplier: 1.5, // 50% faster loading/unloading
        },
      },
      warehouse_complex: {
        id: "warehouse_complex",
        name: "Warehouse Complex",
        description: "Build large warehouses to increase effective ship capacity",
        cost: 8000,
        buildTime: 10,
        effects: {
          capacityBonus: 20, // +20 cargo capacity for ships at this port
        },
      },
      harbor_master_office: {
        id: "harbor_master_office",
        name: "Harbor Master Office",
        description: "Improve port administration for better trade prices",
        cost: 6000,
        buildTime: 6,
        effects: {
          tradeVolumeBonus: 1.1, // 10% better trade prices
        },
      },
      deep_water_berths: {
        id: "deep_water_berths",
        name: "Deep Water Berths",
        description: "Construct deep water berths for larger ships and faster departures",
        cost: 12000,
        buildTime: 15,
        effects: {
          speedBonus: 1.2, // 20% faster departure speed
          capacityBonus: 10, // +10 cargo capacity
        },
      },
      trade_exchange: {
        id: "trade_exchange",
        name: "Trade Exchange",
        description: "Establish a formal trade exchange for optimal pricing",
        cost: 15000,
        buildTime: 12,
        effects: {
          tradeVolumeBonus: 1.25, // 25% better trade prices
          loadingSpeedMultiplier: 1.2, // 20% faster loading
        },
      },
    },

    // Port upgrade requirements
    UPGRADE_REQUIREMENTS: {
      basic_dock_expansion: { populationMin: 2000, portLevel: 0 },
      warehouse_complex: { populationMin: 3000, portLevel: 0, requires: ["basic_dock_expansion"] },
      harbor_master_office: { populationMin: 2500, portLevel: 0 },
      deep_water_berths: { populationMin: 5000, portLevel: 0, requires: ["basic_dock_expansion"] },
      trade_exchange: { populationMin: 6000, portLevel: 0, requires: ["harbor_master_office", "warehouse_complex"] },
    },
  },

  // Player Investment System
  PLAYER_INVESTMENT: {
    // Profit sharing for player-owned industries
    INDUSTRY_PROFIT_SHARE: 0.3, // Player gets 30% of industry profits

    // Investment return calculation
    INVESTMENT_RETURN_MULTIPLIER: 0.05, // 5% of investment value per tick when profitable

    // Requirements for player investments
    MIN_REPUTATION_FOR_INVESTMENT: 60, // Need 60+ reputation to invest

    // Discounts for player investments
    REPUTATION_DISCOUNT: {
      80: 0.9, // 10% discount at 80+ reputation
      90: 0.8, // 20% discount at 90+ reputation
      95: 0.7, // 30% discount at 95+ reputation
    },
  },

  // Trade Route Automation
  TRADE_ROUTES: {
    MAX_ROUTES_PER_PLAYER: 5, // Maximum automated trade routes
    ROUTE_SETUP_COST: 1000, // Cost to establish a trade route

    // Route optimization
    AUTO_OPTIMIZATION: true, // Automatically adjust buy/sell quantities
    OPTIMIZATION_FREQUENCY: 10, // Re-optimize every 10 ticks

    // Route performance
    EFFICIENCY_BONUS: 1.05, // 5% bonus profit for automated routes
    REPUTATION_BONUS_PER_ROUTE: 0.1, // +0.1 reputation per successful route completion
  },

  // Economic Settings using centralized constants
  ECONOMY: {
    PRICE_SUPPLY_FACTOR: ECONOMY.PRICE_SUPPLY_FACTOR,
    PRICE_DEMAND_FACTOR: ECONOMY.PRICE_DEMAND_FACTOR,
    PRICE_MIN_MULTIPLIER: ECONOMY.PRICE_MIN_MULTIPLIER,
    PRICE_MAX_MULTIPLIER: ECONOMY.PRICE_MAX_MULTIPLIER,
    DEMAND_DECAY_RATE: ECONOMY.DEMAND_DECAY_RATE,
    DEMAND_SHORTAGE_MULTIPLIER: ECONOMY.DEMAND_SHORTAGE_MULTIPLIER,
    DEMAND_SURPLUS_MULTIPLIER: ECONOMY.DEMAND_SURPLUS_MULTIPLIER,
    INITIAL_INVENTORY_BASE: ECONOMY.INITIAL_INVENTORY_BASE,
    INITIAL_INVENTORY_RANDOM: ECONOMY.INITIAL_INVENTORY_RANDOM,
    MAX_TRADE_VOLUME_PERCENT: ECONOMY.MAX_TRADE_VOLUME_PERCENT,
  },

  // Population consumption using centralized constants
  POPULATION_CONSUMPTION: {
    BASE_CONSUMPTION_PER_1K_POPULATION: {
      wheat: POPULATION.CONSUMPTION_RATES.WHEAT,
      meat: POPULATION.CONSUMPTION_RATES.MEAT,
      sugar: POPULATION.CONSUMPTION_RATES.SUGAR,
      bread: POPULATION.CONSUMPTION_RATES.BREAD,
      flour: POPULATION.CONSUMPTION_RATES.FLOUR,
      rum: POPULATION.CONSUMPTION_RATES.RUM,
      cloth: POPULATION.CONSUMPTION_RATES.CLOTH,
      fine_cloth: POPULATION.CONSUMPTION_RATES.FINE_CLOTH,
    },

    CITY_WEALTH_MODIFIERS: {
      wealthy: POPULATION.WEALTH_MODIFIERS.WEALTHY,
      poor: POPULATION.WEALTH_MODIFIERS.POOR,
      normal: POPULATION.WEALTH_MODIFIERS.NORMAL,
    },

    CONSUMPTION_VARIANCE: POPULATION.CONSUMPTION_VARIANCE,

    SHORTAGE_ADAPTATION: {
      ENABLED: true,
      SHORTAGE_THRESHOLD: POPULATION.SHORTAGE_THRESHOLD,
      SHORTAGE_CONSUMPTION_MULTIPLIER: POPULATION.SHORTAGE_CONSUMPTION_MULTIPLIER,
    },

    WEALTH_CALCULATION: {
      LUXURY_PRODUCTION_BONUS: 1.2,
      POPULATION_WEALTH_FACTOR: 0.0001,
    },
  },

  // City Settings
  CITIES: {
    // Population effects
    POPULATION_PRODUCTION_MULTIPLIER: 0.001, // How population affects production
    POPULATION_CONSUMPTION_MULTIPLIER: 0.002, // How population affects consumption

    // Economic growth
    GROWTH_RATE_PER_TICK: 0.001, // 0.1% population growth per tick
    MAX_GROWTH_RATE: 0.05, // Maximum 5% growth per tick

    // Production/Consumption variance
    PRODUCTION_VARIANCE: 0.1, // ±10% variance in production per tick
    CONSUMPTION_VARIANCE: 0.05, // ±5% variance in consumption per tick

    // Specialized consumption (in addition to population-based)
    SPECIALIZED_CONSUMPTION: {
      // Tools for production and construction
      tools: 0.5, // Base consumption per 1000 population
      // Wood for construction and fuel
      wood: 1.0,
      // Iron for tools and construction
      iron: 0.3,
      // Weapons for defense
      weapons: 0.2,
    },
  },

  // Ship and Fleet Settings
  SHIPS: {
    // Movement
    BASE_SPEED: 50, // Base movement speed units per tick
    SPEED_VARIANCE: 0.2, // ±20% speed variance between ships

    // Capacity
    SMALL_SHIP_CAPACITY: 50,
    MEDIUM_SHIP_CAPACITY: 100,
    LARGE_SHIP_CAPACITY: 200,

    // Cargo efficiency
    CARGO_LOADING_TIME_TICKS: 1, // Time to load/unload cargo
    CARGO_EFFICIENCY: 0.9, // 90% of theoretical capacity usable
  },

  // Map and UI Settings
  MAP: {
    WIDTH: 400,
    HEIGHT: 350,
    CITY_ICON_SIZE: 32,
    SHIP_ICON_SIZE: 24,

    // Visual effects
    SHIP_TRAIL_LENGTH: 5, // Number of previous positions to show
    ANIMATION_DURATION_MS: 1000, // Smooth animations
  },

  // Goods Categories and Modifiers
  GOODS: {
    CATEGORIES: {
      FOOD: {
        SPOILAGE_RATE: 0.02, // 2% spoilage per tick
        DEMAND_URGENCY: 1.5, // Food demand is more urgent
      },
      MATERIALS: {
        SPOILAGE_RATE: 0, // No spoilage
        DEMAND_URGENCY: 1.0, // Normal demand
      },
      LUXURY: {
        SPOILAGE_RATE: 0, // No spoilage
        DEMAND_URGENCY: 0.7, // Less urgent demand
        PRICE_VOLATILITY: 1.3, // More price swings
      },
      WEAPONS: {
        SPOILAGE_RATE: 0, // No spoilage
        DEMAND_URGENCY: 0.8, // Moderate demand urgency
        PRICE_VOLATILITY: 1.2, // Moderate price swings
      },
    },
  },

  // AI and Automation
  AI: {
    // NPC trader behavior
    NPC_TRADE_FREQUENCY_TICKS: 5, // NPCs trade every 5 ticks
    NPC_PROFIT_MARGIN_TARGET: 0.2, // NPCs target 20% profit margin
    NPC_RISK_TOLERANCE: 0.3, // How much price volatility NPCs accept

    // Market maker behavior
    MARKET_MAKER_ENABLED: true,
    MARKET_MAKER_INTERVENTION_THRESHOLD: 2.0, // Intervene when prices are 2x base
    MARKET_MAKER_VOLUME_PERCENT: 0.1, // Trade 10% of market volume
  },

  // NPC Fleet Management
  NPC_FLEETS: {
    ENABLED: true,

    // Surplus/shortage detection
    SURPLUS_THRESHOLD: 150, // Quantity above which it's considered surplus
    SHORTAGE_THRESHOLD: 20, // Quantity below which it's considered shortage
    PRICE_DIFFERENCE_THRESHOLD: 1.5, // Min price ratio to trigger NPC trade

    // Fleet spawning
    MAX_ACTIVE_NPC_FLEETS: 5, // Maximum NPC fleets active at once
    SPAWN_COOLDOWN_TICKS: 3, // Minimum ticks between spawns
    SPAWN_PROBABILITY: 0.3, // Chance to spawn when conditions are met

    // Fleet composition
    MIN_SHIPS_PER_FLEET: 1, // Minimum ships in an NPC fleet
    MAX_SHIPS_PER_FLEET: 5, // Maximum ships in an NPC fleet
    FLEET_SIZE_WEIGHTS: [0.4, 0.3, 0.15, 0.1, 0.05], // Probability weights for 1,2,3,4,5 ships

    // Fleet characteristics
    CARGO_PERCENTAGE: 0.3, // Take 30% of surplus
    SPEED_VARIANCE: 0.1, // ±10% speed variance from base ship type

    // Trading behavior
    PROFIT_MARGIN_TARGET: 0.15, // Target 15% profit margin
    MAX_TRAVEL_DISTANCE: 300, // Maximum distance NPCs will travel
    UNLOAD_PERCENTAGE: 0.8, // Sell 80% of cargo, keep some for return trip

    // Fleet lifecycle
    DESPAWN_DELAY_TICKS: 2, // Ticks to wait before despawning after mission
    RETURN_TO_ORIGIN: false, // Whether NPCs return to origin city
  },

  // Random Events
  EVENTS: {
    ENABLED: true,
    BASE_PROBABILITY_PER_TICK: 0.01, // 1% chance per tick

    // Event types and their effects
    STORM: {
      PROBABILITY: 0.3,
      SHIP_DELAY_MULTIPLIER: 2.0, // Ships move 50% slower
      DURATION_TICKS: 3,
    },
    BUMPER_HARVEST: {
      PROBABILITY: 0.2,
      PRODUCTION_MULTIPLIER: 1.5, // 50% more production
      DURATION_TICKS: 5,
      AFFECTED_CATEGORIES: ["food"],
    },
    TRADE_EMBARGO: {
      PROBABILITY: 0.1,
      TRADE_RESTRICTION: 0.5, // 50% trade volume reduction
      DURATION_TICKS: 10,
    },
    PIRATE_ATTACK: {
      PROBABILITY: 0.15,
      CARGO_LOSS_PERCENT: 0.2, // Lose 20% of cargo
      AFFECTED_ROUTES: 1, // Number of trade routes affected
    },
  },

  // Performance and Optimization
  PERFORMANCE: {
    MAX_CITIES: 20,
    MAX_SHIPS_PER_FLEET: 10,
    MAX_TRADE_ROUTES: 50,

    // Update frequencies
    PRICE_UPDATE_FREQUENCY: 1, // Update prices every tick
    UI_UPDATE_FREQUENCY: 1, // Update UI every tick
    PATHFINDING_CACHE_SIZE: 100,
  },

  // Debug and Development
  DEBUG: {
    ENABLED: false,
    LOG_ECONOMIC_CHANGES: false,
    LOG_SHIP_MOVEMENTS: false,
    LOG_TRADE_TRANSACTIONS: false,
    LOG_PRODUCTION_CHAINS: false,
    LOG_PLAYER_INVESTMENTS: false,
    SHOW_DEBUG_OVERLAY: false,

    // Cheats for testing
    INSTANT_TRAVEL: false,
    UNLIMITED_CARGO: false,
    FREE_TRADES: false,
    FREE_INVESTMENTS: false,
  },

  // Difficulty Settings
  DIFFICULTY: {
    // Economic challenge
    PRICE_VOLATILITY_MULTIPLIER: 1.0, // 1.0 = normal, 2.0 = very volatile
    COMPETITION_LEVEL: 1.0, // How aggressive NPC traders are

    // Starting conditions
    STARTING_MONEY: 10000,
    STARTING_SHIPS: 1,
    STARTING_REPUTATION: 50, // 0-100 scale

    // Progression
    UNLOCK_REQUIREMENTS: {
      SECOND_SHIP: { money: 5000, reputation: 60 },
      LARGE_SHIPS: { money: 20000, reputation: 80 },
      TRADE_ROUTES: { money: 15000, reputation: 70 },
      CITY_INVESTMENT: { money: 10000, reputation: 60 },
    },
  },

  // Presets for different game modes
  PRESETS: {
    EASY: {
      TICK_INTERVAL_MS: 15000, // Slower pace
      PRICE_VOLATILITY_MULTIPLIER: 0.7,
      STARTING_MONEY: 15000,
      NPC_TRADE_FREQUENCY_TICKS: 8, // Less competition
    },
    NORMAL: {
      TICK_INTERVAL_MS: 10000,
      PRICE_VOLATILITY_MULTIPLIER: 1.0,
      STARTING_MONEY: 10000,
      NPC_TRADE_FREQUENCY_TICKS: 5,
    },
    HARD: {
      TICK_INTERVAL_MS: 5000, // Faster pace
      PRICE_VOLATILITY_MULTIPLIER: 1.5,
      STARTING_MONEY: 5000,
      NPC_TRADE_FREQUENCY_TICKS: 3, // More competition
    },
    SANDBOX: {
      TICK_INTERVAL_MS: 10000,
      STARTING_MONEY: 100000,
      DEBUG: { ENABLED: true },
      EVENTS: { ENABLED: false },
    },
  },
} as const

// Helper function to apply preset
export function applyPreset(presetName: keyof typeof CONFIG.PRESETS) {
  const preset = CONFIG.PRESETS[presetName]
  return {
    ...CONFIG,
    ...preset,
    DEBUG: { ...CONFIG.DEBUG, ...preset.DEBUG },
    EVENTS: { ...CONFIG.EVENTS, ...preset.EVENTS },
  }
}

// Type for configuration
export type GameConfig = typeof CONFIG
