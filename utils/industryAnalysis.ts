import type { City, Industry, Good } from "../types/game"
import { ProductionChainManager } from "../engine/productionChainManager"

export interface IndustryAnalysis {
  status: "active" | "inactive" | "under_construction"
  reasons: string[]
  suggestions: string[]
  efficiency: number
  inputStatus: { [goodId: string]: { available: number; required: number; sufficient: boolean } }
  outputStatus: { [goodId: string]: { buffered: number; cityInventory: number } }
}

export function analyzeIndustry(industry: Industry, city: City, goods: Good[]): IndustryAnalysis {
  const recipe = ProductionChainManager.getRecipe(industry.recipeId)
  const reasons: string[] = []
  const suggestions: string[] = []

  if (!recipe) {
    return {
      status: "inactive",
      reasons: ["Recipe not found"],
      suggestions: ["Contact support - this is a bug"],
      efficiency: 0,
      inputStatus: {},
      outputStatus: {},
    }
  }

  // Analyze input status
  const inputStatus: { [goodId: string]: { available: number; required: number; sufficient: boolean } } = {}
  let hasInsufficientInputs = false

  Object.entries(recipe.inputs).forEach(([goodId, required]) => {
    const buffered = industry.inputBuffer[goodId] || 0
    const cityInventory = city.inventory[goodId]?.quantity || 0
    const available = buffered + cityInventory
    const sufficient = available >= required

    inputStatus[goodId] = {
      available,
      required,
      sufficient,
    }

    if (!sufficient) {
      hasInsufficientInputs = true
      const good = goods.find((g) => g.id === goodId)
      reasons.push(`Insufficient ${good?.name || goodId}: need ${required}, have ${available}`)

      if (cityInventory === 0) {
        suggestions.push(`Import ${good?.name || goodId} from other cities`)
      } else {
        suggestions.push(`Wait for more ${good?.name || goodId} to be produced or imported`)
      }
    }
  })

  // Analyze output status
  const outputStatus: { [goodId: string]: { buffered: number; cityInventory: number } } = {}
  Object.entries(recipe.outputs).forEach(([goodId, _]) => {
    outputStatus[goodId] = {
      buffered: industry.outputBuffer[goodId] || 0,
      cityInventory: city.inventory[goodId]?.quantity || 0,
    }
  })

  // Determine status and reasons
  let status: "active" | "inactive" | "under_construction" = "active"

  if (industry.isUnderConstruction) {
    status = "under_construction"
    reasons.push("Industry is still under construction")
    suggestions.push("Wait for construction to complete")
  } else if (!industry.isActive) {
    status = "inactive"

    if (hasInsufficientInputs) {
      // Already added specific input reasons above
    } else {
      reasons.push("Industry has sufficient inputs but is not producing")
      suggestions.push("Check if there are any system issues or wait for next production cycle")
    }

    // Check for worker shortage (simplified)
    if (city.population < recipe.workerRequirement * 2) {
      reasons.push(`Low population: need ~${recipe.workerRequirement * 2} people for optimal operation`)
      suggestions.push("Wait for city population to grow")
    }

    // Check for efficiency issues
    if (industry.efficiency < 0.5) {
      reasons.push(`Low efficiency (${(industry.efficiency * 100).toFixed(0)}%) due to city conditions`)
      suggestions.push("Improve city wealth and population for better efficiency")
    }
  }

  return {
    status,
    reasons,
    suggestions,
    efficiency: industry.efficiency,
    inputStatus,
    outputStatus,
  }
}

export function getIndustryStatusColor(status: "active" | "inactive" | "under_construction"): string {
  switch (status) {
    case "active":
      return "text-green-600 bg-green-100"
    case "under_construction":
      return "text-yellow-600 bg-yellow-100"
    case "inactive":
      return "text-red-600 bg-red-100"
  }
}

export function getIndustryStatusText(status: "active" | "inactive" | "under_construction"): string {
  switch (status) {
    case "active":
      return "Active"
    case "under_construction":
      return "Building"
    case "inactive":
      return "Inactive"
  }
}
