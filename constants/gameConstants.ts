/**
 * Game Constants - All hardcoded values used throughout the game
 *
 * This file centralizes all magic numbers, thresholds, and configuration values
 * to make them easy to find, modify, and understand.
 */

// =============================================================================
// CORE GAME MECHANICS
// =============================================================================

/**
 * Time-based constants for game progression
 */
export const TIME_CONSTANTS = {
  /** Base interval between game ticks in milliseconds (10 seconds) */
  DEFAULT_TICK_INTERVAL_MS: 100,

  /** How often the game auto-saves progress (every 10 ticks) */
  AUTO_SAVE_INTERVAL_TICKS: 10,

  /** Minimum time between NPC fleet spawns (3 ticks = 30 seconds) */
  NPC_SPAWN_COOLDOWN_TICKS: 3,

  /** How long NPC fleets wait before despawning after completing missions */
  NPC_DESPAWN_DELAY_TICKS: 2,
} as const

/**
 * Ship movement and travel constants
 */
export const SHIP_CONSTANTS = {
  /** Base movement speed for ships (units per tick) */
  BASE_SPEED: 50,

  /** Random variance applied to ship speeds (±20%) */
  SPEED_VARIANCE: 0.2,

  /** Progress value indicating travel completion (0-1 scale) */
  TRAVEL_COMPLETION_THRESHOLD: 1.0,

  /** Time in ticks required to load/unload cargo */
  CARGO_LOADING_TIME_TICKS: 1,

  /** Percentage of theoretical capacity that's actually usable */
  CARGO_EFFICIENCY: 0.9,
} as const

/**
 * Ship capacity constants by size category
 */
export const SHIP_CAPACITY = {
  /** Small ships like sloops */
  SMALL: 50,

  /** Medium ships like brigantines */
  MEDIUM: 100,

  /** Large ships like galleons */
  LARGE: 200,
} as const

// =============================================================================
// ECONOMIC SYSTEM
// =============================================================================

/**
 * Price calculation and market dynamics
 */
export const ECONOMY_CONSTANTS = {
  /** How sensitive prices are to supply levels (higher = more sensitive) */
  PRICE_SUPPLY_FACTOR: 100,

  /** How sensitive prices are to demand levels (higher = more sensitive) */
  PRICE_DEMAND_FACTOR: 100,

  /** Minimum price as percentage of base price (10% minimum) */
  PRICE_MIN_MULTIPLIER: 0.1,

  /** Maximum price as percentage of base price (500% maximum) */
  PRICE_MAX_MULTIPLIER: 5.0,

  /** How quickly demand returns to normal each tick (5% decay) */
  DEMAND_DECAY_RATE: 0.95,

  /** How much shortages increase demand */
  DEMAND_SHORTAGE_MULTIPLIER: 0.1,

  /** How much surpluses decrease demand */
  DEMAND_SURPLUS_MULTIPLIER: 0.1,

  /** Base inventory amount for cities */
  INITIAL_INVENTORY_BASE: 50,

  /** Random additional inventory (0-100) */
  INITIAL_INVENTORY_RANDOM: 100,

  /** Maximum percentage of city inventory tradeable per transaction */
  MAX_TRADE_VOLUME_PERCENT: 0.5,
} as const

/**
 * Trading and reputation system
 */
export const TRADING_CONSTANTS = {
  /** Maximum number of trade transactions to keep in history */
  MAX_TRADE_HISTORY_ITEMS: 100,

  /** How much reputation affects trade prices (±10% at extremes) */
  REPUTATION_PRICE_MODIFIER: 0.002,

  /** Reputation gain per dollar of trade value */
  REPUTATION_GAIN_PER_TRADE_VALUE: 0.0001,

  /** Maximum reputation gain per single trade */
  MAX_REPUTATION_GAIN_PER_TRADE: 0.1,

  /** Minimum reputation value */
  MIN_REPUTATION: 0,

  /** Maximum reputation value */
  MAX_REPUTATION: 100,
} as const

// =============================================================================
// POPULATION AND CONSUMPTION
// =============================================================================

/**
 * Population-based consumption rates per 1000 people per tick
 * These represent how much each good is consumed by city populations
 */
export const POPULATION_CONSUMPTION_RATES = {
  /** Essential food goods */
  WHEAT: 2.5, // Bread/grain - most essential food
  MEAT: 1.8, // Protein source
  SUGAR: 1.2, // Sweetener and preservative
  BREAD: 1.5, // Processed food (preferred over wheat)
  FLOUR: 0.5, // Used for cooking

  /** Luxury and comfort goods */
  RUM: 0.8, // Alcohol - social lubricant in Caribbean
  CLOTH: 0.6, // Clothing and textiles
  FINE_CLOTH: 0.2, // Luxury textiles
} as const

/**
 * Consumption modifiers based on city wealth levels
 */
export const WEALTH_CONSUMPTION_MODIFIERS = {
  /** Wealthy cities consume more luxury goods */
  WEALTHY: {
    rum: 1.5,
    cloth: 1.8,
    sugar: 1.3,
    wheat: 1.1,
    meat: 1.2,
    fine_cloth: 2.0,
    bread: 1.3,
  },

  /** Poor cities consume less luxury, focus on essentials */
  POOR: {
    rum: 0.6,
    cloth: 0.4,
    sugar: 0.7,
    wheat: 0.9,
    meat: 0.8,
    fine_cloth: 0.1,
    bread: 0.8,
  },

  /** Normal cities use base rates */
  NORMAL: {
    rum: 1.0,
    cloth: 1.0,
    sugar: 1.0,
    wheat: 1.0,
    meat: 1.0,
    fine_cloth: 0.5,
    bread: 1.0,
  },
} as const

/**
 * Specialized consumption rates (tools, materials, etc.)
 */
export const SPECIALIZED_CONSUMPTION_RATES = {
  /** Tools for production and construction per 1000 population */
  TOOLS: 0.5,

  /** Wood for construction and fuel per 1000 population */
  WOOD: 1.0,

  /** Iron for tools and construction per 1000 population */
  IRON: 0.3,

  /** Weapons for defense per 1000 population */
  WEAPONS: 0.2,
} as const

/**
 * Population and city growth constants
 */
export const POPULATION_CONSTANTS = {
  /** Base population growth rate per tick (0.1%) */
  GROWTH_RATE_PER_TICK: 0.001,

  /** Maximum population growth rate per tick (5%) */
  MAX_GROWTH_RATE: 0.05,

  /** Random variance in consumption (±15%) */
  CONSUMPTION_VARIANCE: 0.15,

  /** Threshold below which consumption is reduced due to shortage */
  SHORTAGE_THRESHOLD: 10,

  /** Consumption multiplier when goods are scarce (30% of normal) */
  SHORTAGE_CONSUMPTION_MULTIPLIER: 0.3,
} as const

// =============================================================================
// PRODUCTION AND INDUSTRIES
// =============================================================================

/**
 * Industry efficiency and production modifiers
 */
export const PRODUCTION_CONSTANTS = {
  /** Efficiency bonus for wealthy cities (+20%) */
  WEALTHY_CITY_EFFICIENCY_BONUS: 1.2,

  /** Efficiency penalty for poor cities (-20%) */
  POOR_CITY_EFFICIENCY_PENALTY: 0.8,

  /** Efficiency bonus for large cities (+15%) */
  LARGE_CITY_EFFICIENCY_BONUS: 1.15,

  /** Efficiency penalty for small cities (-10%) */
  SMALL_CITY_EFFICIENCY_PENALTY: 0.9,

  /** Random efficiency variance per tick (±10%) */
  EFFICIENCY_VARIANCE: 0.1,

  /** Efficiency bonus for player-owned industries (+10%) */
  PLAYER_OWNERSHIP_BONUS: 1.1,

  /** Random variance in city production (±10%) */
  PRODUCTION_VARIANCE: 0.1,

  /** Chance per tick for cities to develop new industries (5%) */
  INDUSTRY_DEVELOPMENT_CHANCE: 0.05,
} as const

/**
 * Industry construction time by category (in ticks)
 */
export const CONSTRUCTION_TIME = {
  /** Food processing industries (mills, bakeries, distilleries) */
  FOOD_PROCESSING: 3,

  /** Manufacturing industries (forges, armories) */
  MANUFACTURING: 5,

  /** Advanced manufacturing (shipyards, complex facilities) */
  ADVANCED_MANUFACTURING: 10,
} as const

/**
 * Population and wealth requirements for industry categories
 */
export const INDUSTRY_REQUIREMENTS = {
  /** Primary industries (resource extraction) */
  PRIMARY: {
    POPULATION: 1000,
    WEALTH: 0,
  },

  /** Food processing industries */
  FOOD_PROCESSING: {
    POPULATION: 2000,
    WEALTH: 5000,
  },

  /** Manufacturing industries */
  MANUFACTURING: {
    POPULATION: 3000,
    WEALTH: 10000,
  },

  /** Advanced manufacturing */
  ADVANCED_MANUFACTURING: {
    POPULATION: 5000,
    WEALTH: 25000,
  },
} as const

// =============================================================================
// INVESTMENT SYSTEM
// =============================================================================

/**
 * Player investment mechanics
 */
export const INVESTMENT_CONSTANTS = {
  /** Minimum reputation required to make investments */
  MIN_REPUTATION_FOR_INVESTMENT: 60,

  /** Player's share of industry profits (30%) */
  INDUSTRY_PROFIT_SHARE: 0.3,

  /** Investment return multiplier (5% of investment value per tick when profitable) */
  INVESTMENT_RETURN_MULTIPLIER: 0.05,
} as const

/**
 * Reputation-based investment discounts
 */
export const REPUTATION_DISCOUNTS = {
  /** 10% discount at 80+ reputation */
  REPUTATION_80: 0.9,

  /** 20% discount at 90+ reputation */
  REPUTATION_90: 0.8,

  /** 30% discount at 95+ reputation */
  REPUTATION_95: 0.7,
} as const

// =============================================================================
// NPC FLEET SYSTEM
// =============================================================================

/**
 * NPC fleet behavior and spawning
 */
export const NPC_FLEET_CONSTANTS = {
  /** Maximum number of NPC fleets active simultaneously */
  MAX_ACTIVE_NPC_FLEETS: 5,

  /** Probability of spawning NPC fleet when conditions are met (30%) */
  SPAWN_PROBABILITY: 0.3,

  /** Quantity threshold above which a city has surplus */
  SURPLUS_THRESHOLD: 150,

  /** Quantity threshold below which a city has shortage */
  SHORTAGE_THRESHOLD: 20,

  /** Minimum price ratio to trigger NPC trading (50% price difference) */
  PRICE_DIFFERENCE_THRESHOLD: 1.5,

  /** Percentage of surplus that NPCs will trade (30%) */
  CARGO_PERCENTAGE: 0.3,

  /** Speed variance for NPC ships (±10%) */
  SPEED_VARIANCE: 0.1,

  /** Target profit margin for NPC trades (15%) */
  PROFIT_MARGIN_TARGET: 0.15,

  /** Maximum distance NPCs will travel for trades */
  MAX_TRAVEL_DISTANCE: 300,

  /** Percentage of cargo NPCs sell at destination (80%) */
  UNLOAD_PERCENTAGE: 0.8,
} as const

/**
 * NPC fleet size probability weights
 * Index 0 = 1 ship, Index 1 = 2 ships, etc.
 */
export const NPC_FLEET_SIZE_WEIGHTS = [0.4, 0.3, 0.15, 0.1, 0.05] as const

// =============================================================================
// UI AND VISUAL CONSTANTS
// =============================================================================

/**
 * Map and visual display constants
 */
export const UI_CONSTANTS = {
  /** Game map width in pixels */
  MAP_WIDTH: 400,

  /** Game map height in pixels */
  MAP_HEIGHT: 350,

  /** Size of city icons on map */
  CITY_ICON_SIZE: 32,

  /** Size of ship icons on map */
  SHIP_ICON_SIZE: 24,

  /** Number of previous ship positions to show in trail */
  SHIP_TRAIL_LENGTH: 5,

  /** Duration of smooth animations in milliseconds */
  ANIMATION_DURATION_MS: 1000,

  /** Maximum number of price history points to keep */
  MAX_PRICE_HISTORY_POINTS: 20,
} as const

// =============================================================================
// DIFFICULTY AND GAME BALANCE
// =============================================================================

/**
 * Starting conditions and difficulty settings
 */
export const DIFFICULTY_CONSTANTS = {
  /** Starting money for new players */
  STARTING_MONEY: 10000,

  /** Starting reputation (0-100 scale) */
  STARTING_REPUTATION: 50,

  /** Starting number of ships */
  STARTING_SHIPS: 1,

  /** Price volatility multiplier (1.0 = normal, 2.0 = very volatile) */
  PRICE_VOLATILITY_MULTIPLIER: 1.0,

  /** How aggressive NPC traders are (1.0 = normal competition) */
  COMPETITION_LEVEL: 1.0,
} as const

/**
 * Unlock requirements for game features
 */
export const UNLOCK_REQUIREMENTS = {
  /** Requirements for purchasing a second ship */
  SECOND_SHIP: {
    MONEY: 5000,
    REPUTATION: 60,
  },

  /** Requirements for accessing large ships */
  LARGE_SHIPS: {
    MONEY: 20000,
    REPUTATION: 80,
  },

  /** Requirements for automated trade routes */
  TRADE_ROUTES: {
    MONEY: 15000,
    REPUTATION: 70,
  },

  /** Requirements for city investments */
  CITY_INVESTMENT: {
    MONEY: 10000,
    REPUTATION: 60,
  },
} as const

// =============================================================================
// PERFORMANCE AND LIMITS
// =============================================================================

/**
 * Performance limits and optimization constants
 */
export const PERFORMANCE_CONSTANTS = {
  /** Maximum number of cities in the game */
  MAX_CITIES: 20,

  /** Maximum ships per fleet */
  MAX_SHIPS_PER_FLEET: 10,

  /** Maximum number of trade routes */
  MAX_TRADE_ROUTES: 50,

  /** How often to update prices (every tick) */
  PRICE_UPDATE_FREQUENCY: 1,

  /** How often to update UI (every tick) */
  UI_UPDATE_FREQUENCY: 1,

  /** Size of pathfinding cache */
  PATHFINDING_CACHE_SIZE: 100,
} as const

// =============================================================================
// DEBUG AND DEVELOPMENT
// =============================================================================

/**
 * Debug mode constants and development helpers
 */
export const DEBUG_CONSTANTS = {
  /** Whether debug mode is enabled */
  ENABLED: false,

  /** Log economic changes to console */
  LOG_ECONOMIC_CHANGES: false,

  /** Log ship movements to console */
  LOG_SHIP_MOVEMENTS: false,

  /** Log trade transactions to console */
  LOG_TRADE_TRANSACTIONS: false,

  /** Log production chain activities */
  LOG_PRODUCTION_CHAINS: false,

  /** Log player investment activities */
  LOG_PLAYER_INVESTMENTS: false,

  /** Show debug overlay on screen */
  SHOW_DEBUG_OVERLAY: false,

  /** Enable instant travel for testing */
  INSTANT_TRAVEL: false,

  /** Remove cargo capacity limits */
  UNLIMITED_CARGO: false,

  /** Make all trades free */
  FREE_TRADES: false,

  /** Make all investments free */
  FREE_INVESTMENTS: false,
} as const

// =============================================================================
// EXPORT GROUPS FOR CONVENIENCE
// =============================================================================

/** All time-related constants */
export const TIME = TIME_CONSTANTS

/** All ship-related constants */
export const SHIPS = {
  ...SHIP_CONSTANTS,
  CAPACITY: SHIP_CAPACITY,
}

/** All economy-related constants */
export const ECONOMY = {
  ...ECONOMY_CONSTANTS,
  TRADING: TRADING_CONSTANTS,
}

/** All population-related constants */
export const POPULATION = {
  CONSUMPTION_RATES: POPULATION_CONSUMPTION_RATES,
  WEALTH_MODIFIERS: WEALTH_CONSUMPTION_MODIFIERS,
  SPECIALIZED_RATES: SPECIALIZED_CONSUMPTION_RATES,
  ...POPULATION_CONSTANTS,
}

/** All production-related constants */
export const PRODUCTION = {
  ...PRODUCTION_CONSTANTS,
  CONSTRUCTION_TIME,
  REQUIREMENTS: INDUSTRY_REQUIREMENTS,
}

/** All investment-related constants */
export const INVESTMENT = {
  ...INVESTMENT_CONSTANTS,
  DISCOUNTS: REPUTATION_DISCOUNTS,
}

/** All NPC-related constants */
export const NPC = {
  ...NPC_FLEET_CONSTANTS,
  FLEET_SIZE_WEIGHTS: NPC_FLEET_SIZE_WEIGHTS,
}

/** All UI-related constants */
export const UI = UI_CONSTANTS

/** All difficulty-related constants */
export const DIFFICULTY = {
  ...DIFFICULTY_CONSTANTS,
  UNLOCKS: UNLOCK_REQUIREMENTS,
}

/** All performance-related constants */
export const PERFORMANCE = PERFORMANCE_CONSTANTS

/** All debug-related constants */
export const DEBUG = DEBUG_CONSTANTS
