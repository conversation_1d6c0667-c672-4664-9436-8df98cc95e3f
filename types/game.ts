// Update to use the new organized type imports
import type { GameState } from "./core/gameState"
import type { City, Ship, Fleet, NPCFleet, Good } from "./core/entities"
import type { Industry, ProductionRecipe } from "./core/production"
import type { TradeTransaction, TradeRoute, MarketOpportunity } from "./core/economy"
import type { PlayerStats, PlayerInvestment, PortUpgrade, CityPort } from "./core/investment"

// Re-export all types for backward compatibility
export type {
  GameState,
  City,
  Ship,
  Fleet,
  NPCFleet,
  Good,
  Industry,
  ProductionRecipe,
  TradeTransaction,
  TradeRoute,
  MarketOpportunity,
  PlayerStats,
  PlayerInvestment,
  PortUpgrade,
  CityPort,
}
