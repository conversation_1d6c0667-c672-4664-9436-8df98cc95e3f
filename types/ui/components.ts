import type React from "react"
/**
 * UI component prop types and interface definitions
 */

import type { City, Ship, Good } from "../core/entities"
import type { PlayerStats } from "../core/investment"

export interface TradingInterfaceProps {
  ship: Ship
  city: City
  goods: Good[]
  player: PlayerStats
  onTrade: (goodId: string, quantity: number, action: "buy" | "sell") => void
  onGetTradeRecommendations: () => TradeRecommendation[]
}

export interface TradeRecommendation {
  goodId: string
  action: "buy" | "sell"
  quantity: number
  currentPrice: number
  targetCityId: string
  targetPrice: number
  profitPotential: number
  confidence: "high" | "medium" | "low"
  distance: number
}

export interface InvestmentOpportunity {
  type: "industry" | "port_upgrade"
  id: string
  name: string
  description: string
  cost: number
  buildTime: number
  requirements: string[]
  canAfford: boolean
  canBuild: boolean
  expectedReturns: number
}

export interface InvestmentSummary {
  totalInvested: number
  totalReturns: number
  activeInvestments: number
  completedInvestments: number
  activeInvestmentsList: any[]
  monthlyReturns: number
}

export interface CityDetailsPanelProps {
  city: City
  goods: Good[]
  priceHistory: { [goodId: string]: number[] }
  consumptionBreakdown: {
    population: { [goodId: string]: number }
    specialized: { [goodId: string]: number }
    total: { [goodId: string]: number }
    wealthLevel: "poor" | "normal" | "wealthy"
  }
  industries: any[]
  showInvestmentTab?: boolean
  investmentContent?: React.ReactNode
}

export interface FleetOverviewPanelProps {
  ships: Ship[]
  cities: City[]
  onSelectShip: (shipId: string) => void
  selectedShip: string | null
}
