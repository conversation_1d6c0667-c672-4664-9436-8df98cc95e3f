export interface ShipType {
  id: string
  name: string
  description: string
  speed: number // Base speed units per tick
  cargoCapacity: number
  cost: number // For future player ship purchasing
  rarity: number // 0-1, affects spawn probability
  era: string
  advantages: string[]
  disadvantages: string[]
}

export const SHIP_TYPES: ShipType[] = [
  {
    id: "sloop",
    name: "Sloop",
    description: "A fast, single-masted vessel perfect for quick coastal trading",
    speed: 70,
    cargoCapacity: 40,
    cost: 2000,
    rarity: 0.4, // Common
    era: "Colonial",
    advantages: ["High speed", "Low crew requirements", "Excellent maneuverability"],
    disadvantages: ["Limited cargo space", "Vulnerable in storms"],
  },
  {
    id: "brigantine",
    name: "<PERSON>rigantine",
    description: "A versatile two-masted vessel, the workhorse of Caribbean trade",
    speed: 55,
    cargoCapacity: 80,
    cost: 4500,
    rarity: 0.3, // Common
    era: "Colonial",
    advantages: ["Balanced speed and cargo", "Good in various weather", "Reliable"],
    disadvantages: ["Jack of all trades, master of none"],
  },
  {
    id: "galleon",
    name: "Galleon",
    description: "A massive treasure ship capable of carrying enormous cargo loads",
    speed: 35,
    cargoCapacity: 200,
    cost: 12000,
    rarity: 0.1, // Rare
    era: "Spanish Colonial",
    advantages: ["Massive cargo capacity", "Heavily armed", "Prestigious"],
    disadvantages: ["Very slow", "Requires large crew", "Expensive to maintain"],
  },
  {
    id: "frigate",
    name: "Frigate",
    description: "A swift warship adapted for merchant use, fast and well-armed",
    speed: 65,
    cargoCapacity: 60,
    cost: 8000,
    rarity: 0.15, // Uncommon
    era: "Age of Sail",
    advantages: ["High speed", "Well-armed", "Good in combat"],
    disadvantages: ["Moderate cargo space", "High maintenance"],
  },
  {
    id: "merchant_ship",
    name: "Merchant Ship",
    description: "A purpose-built trading vessel optimized for cargo transport",
    speed: 45,
    cargoCapacity: 120,
    cost: 6500,
    rarity: 0.25, // Uncommon
    era: "Colonial",
    advantages: ["Large cargo hold", "Efficient design", "Cost-effective"],
    disadvantages: ["Slow speed", "Lightly armed", "Vulnerable to pirates"],
  },
]

// Helper function to get ship type by ID
export function getShipType(id: string): ShipType | undefined {
  return SHIP_TYPES.find((type) => type.id === id)
}

// Helper function to select random ship type based on rarity
export function selectRandomShipType(): ShipType {
  const totalRarity = SHIP_TYPES.reduce((sum, type) => sum + type.rarity, 0)
  let random = Math.random() * totalRarity

  for (const shipType of SHIP_TYPES) {
    random -= shipType.rarity
    if (random <= 0) {
      return shipType
    }
  }

  // Fallback to brigantine if something goes wrong
  return SHIP_TYPES.find((type) => type.id === "brigantine")!
}
