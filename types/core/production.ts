/**
 * Production system types for industries and manufacturing
 */

export interface ProductionRecipe {
  id: string
  name: string
  category: string
  inputs: { [goodId: string]: number }
  outputs: { [goodId: string]: number }
  baseEfficiency: number
  processingTime: number
  workerRequirement: number
  upkeepCost: number
  description: string
  buildCost: number // Cost for player to build this industry
  buildTime: number // Time in ticks to construct
  buildMaterials?: { [goodId: string]: number } // Required materials to build
}

export interface Industry {
  id: string
  recipeId: string
  name: string
  efficiency: number // Current efficiency (can be modified by city conditions)
  isActive: boolean
  inputBuffer: { [goodId: string]: number } // Stored inputs waiting to be processed
  outputBuffer: { [goodId: string]: number } // Processed outputs waiting to be distributed
  processingProgress: number // 0-1, for multi-tick recipes
  lastProcessedTick: number
  constructionStartTick?: number // If currently being built
  isUnderConstruction: boolean
  // Player investment tracking
  isPlayerOwned: boolean // Whether player invested in this industry
  playerInvestment: number // Amount player invested
  playerProfitShare: number // Percentage of profits player receives (0-1)
}
