/**
 * Main game state interface that combines all game systems
 */

import type { City, Fleet, NPCFleet, Good } from "./entities"
import type { TradeRoute } from "./economy"
import type { PlayerStats } from "./investment"

export interface GameState {
  cities: City[]
  fleets: Fleet[]
  npcFleets: NPCFleet[]
  tradeRoutes: TradeRoute[]
  goods: Good[]
  currentTick: number
  isRunning: boolean
  tickInterval: number
  lastNPCSpawnTick: number
  player: PlayerStats
}
