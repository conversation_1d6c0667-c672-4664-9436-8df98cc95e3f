/**
 * Core entity definitions for the Port Royale Economic Simulator
 * These represent the fundamental objects in the game world
 */

export interface Position {
  x: number
  y: number
}

export interface Good {
  id: string
  name: string
  basePrice: number
  category: "food" | "materials" | "luxury" | "weapons"
}

export interface CityInventory {
  [goodId: string]: {
    quantity: number
    price: number
    demand: number // negative = surplus, positive = shortage
  }
}

export interface Ship {
  id: string
  name: string
  typeId: string // Reference to ship type
  capacity: number // Actual capacity (may differ from base type)
  speed: number // Actual speed (may differ from base type)
  cargo: { [goodId: string]: number }
  currentCity?: string
  destination?: string
  progress: number // 0-1 for travel progress
  // Trade route assignment
  assignedRoute?: string // ID of trade route this ship is following
  isNPC?: boolean // Add this to distinguish NPC ships
}

export interface Fleet {
  id: string
  name: string
  ships: Ship[]
  owner: string
  // Fleet-level properties calculated from ships
  totalCapacity?: number
  effectiveSpeed?: number
}

export interface NPCFleet extends Fleet {
  mission: {
    originCityId: string
    destinationCityId: string
    goodId: string
    targetQuantity: number
    expectedProfit: number
    status: "traveling_to_destination" | "trading" | "mission_complete" | "returning"
    completionTick?: number
  }
  isNPC: true
}

export interface Industry {
  id: string
  name: string
  productionRate: number
  goodId: string
}

export interface CityPort {
  capacity: number
  efficiency: number
}

export interface City {
  id: string
  name: string
  position: Position
  population: number
  wealth: number // Accumulated wealth for building industries
  inventory: CityInventory
  production: { [goodId: string]: number } // Legacy raw production (will be phased out)
  consumption: { [goodId: string]: number } // Will be calculated dynamically
  industries: Industry[] // New production chain industries
  // City characteristics
  specialization?: string // What the city is known for
  developmentLevel: "small" | "medium" | "large" // Based on population
  // Port infrastructure
  port: CityPort
  // Player investment opportunities
  availableInvestments: string[] // Recipe IDs that player can invest in
}
