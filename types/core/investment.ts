/**
 * Investment system types for player city development
 */

import type { TradeTransaction } from "./trade" // Assuming TradeTransaction is defined in another file

export interface PlayerInvestment {
  id: string
  type: "industry" | "port_upgrade"
  cityId: string
  targetId: string // Industry recipe ID or port upgrade ID
  investmentAmount: number
  startTick: number
  completionTick?: number
  status: "under_construction" | "completed" | "cancelled"
  expectedReturns: number // Expected profit per tick
}

export interface PortUpgrade {
  id: string
  name: string
  description: string
  cost: number
  buildTime: number
  effects: {
    loadingSpeedMultiplier?: number // Faster cargo loading/unloading
    capacityBonus?: number // Extra cargo capacity for ships at this port
    speedBonus?: number // Faster departure from this port
    tradeVolumeBonus?: number // Better trade prices due to improved facilities
  }
}

export interface CityPort {
  level: number // 0 = basic, 1+ = upgraded levels
  upgrades: string[] // IDs of completed upgrades
  underConstruction?: {
    upgradeId: string
    startTick: number
    completionTick: number
  }
}

export interface PlayerStats {
  money: number
  totalProfit: number
  totalTrades: number
  reputation: number // 0-100, affects trade prices and opportunities
  tradeHistory: TradeTransaction[]
  // Investment tracking
  activeInvestments: PlayerInvestment[]
  totalInvested: number
  investmentReturns: number // Total returns from investments
}
