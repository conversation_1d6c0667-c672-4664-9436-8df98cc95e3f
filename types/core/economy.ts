/**
 * Economic system types for trading, markets, and financial operations
 */

export interface TradeTransaction {
  id: string
  tick: number
  shipId: string
  cityId: string
  goodId: string
  quantity: number
  pricePerUnit: number
  totalValue: number
  action: "buy" | "sell"
  profit?: number // For sell transactions
}

export interface MarketOpportunity {
  surplusCity: string
  shortageCity: string
  goodId: string
  surplusQuantity: number
  shortageQuantity: number
  priceDifference: number
  profitPotential: number
  distance: number
}

export interface TradeRoute {
  id: string
  name: string
  fleetId: string
  isActive: boolean
  cities: string[]
  currentCityIndex: number
  tradeOrders: {
    cityId: string
    goodId: string
    action: "buy" | "sell"
    quantity: number
    maxPrice?: number
    minPrice?: number
  }[]
  // Performance tracking
  totalProfit: number
  completedRounds: number
  lastCompletedTick: number
}
