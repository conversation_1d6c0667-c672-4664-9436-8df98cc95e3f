import type { Good, City, Fleet, GameState } from "../types/game"
import { CONFIG } from "../config/config"
import { getShipType } from "../types/shipTypes"

export const GOODS: Good[] = [
  // Basic foods
  { id: "wheat", name: "Wheat", basePrice: 10, category: "food" },
  { id: "meat", name: "Meat", basePrice: 25, category: "food" },
  { id: "sugar", name: "Sugar", basePrice: 15, category: "food" },

  // Processed foods
  { id: "flour", name: "Flour", basePrice: 18, category: "food" },
  { id: "bread", name: "Bread", basePrice: 22, category: "food" },
  { id: "rum", name: "Rum", basePrice: 40, category: "luxury" },

  // Materials
  { id: "wood", name: "Wood", basePrice: 8, category: "materials" },
  { id: "iron", name: "Iron", basePrice: 30, category: "materials" },
  { id: "tools", name: "Tools", basePrice: 50, category: "materials" },

  // Textiles
  { id: "cloth", name: "Cloth", basePrice: 20, category: "luxury" },
  { id: "fine_cloth", name: "Fine Cloth", basePrice: 45, category: "luxury" },

  // Advanced goods
  { id: "weapons", name: "Weapons", basePrice: 80, category: "weapons" },
  { id: "ship_parts", name: "Ship Parts", basePrice: 200, category: "materials" },
]

export const INITIAL_CITIES: City[] = [
  {
    id: "port-royal",
    name: "Port Royal",
    position: { x: 200, y: 150 },
    population: 5000,
    wealth: 15000,
    inventory: {},
    production: { sugar: 15 }, // Legacy production - will be replaced by industries
    consumption: {},
    industries: [
      {
        id: "port-royal-rum-distillery-0",
        recipeId: "rum_distillery",
        name: "Port Royal Rum Distillery",
        efficiency: 0.85,
        isActive: true,
        isUnderConstruction: false,
        inputBuffer: {},
        outputBuffer: {},
        processingProgress: 0,
        lastProcessedTick: 0,
        isPlayerOwned: false,
        playerInvestment: 0,
        playerProfitShare: 0,
      },
    ],
    developmentLevel: "large",
    port: {
      level: 0,
      upgrades: [],
    },
    availableInvestments: ["flour_mill", "bakery", "textile_mill"],
  },
  {
    id: "havana",
    name: "Havana",
    position: { x: 100, y: 200 },
    population: 8000,
    wealth: 25000,
    inventory: {},
    production: { sugar: 25, cloth: 10 },
    consumption: {},
    industries: [
      {
        id: "havana-textile-mill-0",
        recipeId: "textile_mill",
        name: "Havana Textile Mill",
        efficiency: 0.8,
        isActive: true,
        isUnderConstruction: false,
        inputBuffer: {},
        outputBuffer: {},
        processingProgress: 0,
        lastProcessedTick: 0,
        isPlayerOwned: false,
        playerInvestment: 0,
        playerProfitShare: 0,
      },
    ],
    developmentLevel: "large",
    port: {
      level: 0,
      upgrades: [],
    },
    availableInvestments: ["rum_distillery", "forge", "armory"],
  },
  {
    id: "tortuga",
    name: "Tortuga",
    position: { x: 150, y: 100 },
    population: 2000,
    wealth: 8000,
    inventory: {},
    production: { wood: 30, meat: 15 },
    consumption: {},
    industries: [],
    developmentLevel: "medium",
    port: {
      level: 0,
      upgrades: [],
    },
    availableInvestments: ["flour_mill", "forge"],
  },
  {
    id: "cartagena",
    name: "Cartagena",
    position: { x: 50, y: 300 },
    population: 6000,
    wealth: 18000,
    inventory: {},
    production: { wheat: 35, iron: 12 },
    consumption: {},
    industries: [
      {
        id: "cartagena-flour-mill-0",
        recipeId: "flour_mill",
        name: "Cartagena Flour Mill",
        efficiency: 0.9,
        isActive: true,
        isUnderConstruction: false,
        inputBuffer: {},
        outputBuffer: {},
        processingProgress: 0,
        lastProcessedTick: 0,
        isPlayerOwned: false,
        playerInvestment: 0,
        playerProfitShare: 0,
      },
    ],
    developmentLevel: "large",
    port: {
      level: 0,
      upgrades: [],
    },
    availableInvestments: ["bakery", "armory", "shipyard"],
  },
  {
    id: "nassau",
    name: "Nassau",
    position: { x: 300, y: 120 },
    population: 3000,
    wealth: 12000,
    inventory: {},
    production: { cloth: 12 },
    consumption: {},
    industries: [
      {
        id: "nassau-forge-0",
        recipeId: "forge",
        name: "Nassau Forge",
        efficiency: 0.75,
        isActive: true,
        isUnderConstruction: false,
        inputBuffer: {},
        outputBuffer: {},
        processingProgress: 0,
        lastProcessedTick: 0,
        isPlayerOwned: false,
        playerInvestment: 0,
        playerProfitShare: 0,
      },
    ],
    developmentLevel: "medium",
    port: {
      level: 0,
      upgrades: [],
    },
    availableInvestments: ["textile_mill", "armory"],
  },
]

export const INITIAL_FLEETS: Fleet[] = [
  {
    id: "player-fleet-1",
    name: "Trading Fleet Alpha",
    owner: "player",
    ships: [
      {
        id: "ship-1",
        name: "Merchant's Dream",
        typeId: "brigantine",
        capacity: getShipType("brigantine")?.cargoCapacity || 80,
        speed: getShipType("brigantine")?.speed || 55,
        cargo: {},
        currentCity: "port-royal",
      },
    ],
  },
]

// Initialize city inventories
export function initializeCityInventories(cities: City[]): City[] {
  return cities.map((city) => ({
    ...city,
    inventory: GOODS.reduce((inv, good) => {
      const baseQuantity =
        CONFIG.ECONOMY.INITIAL_INVENTORY_BASE + Math.random() * CONFIG.ECONOMY.INITIAL_INVENTORY_RANDOM
      const isProduced = city.production[good.id] > 0

      let demand = 0
      // Population goods always have some base demand
      const populationGoods = ["wheat", "meat", "rum", "sugar", "cloth", "bread", "flour", "fine_cloth"]
      if (populationGoods.includes(good.id)) {
        demand = 10 // Base demand for population goods
      } else if (isProduced) {
        demand = -10 // Surplus for produced goods
      }

      inv[good.id] = {
        quantity: Math.floor(baseQuantity),
        price: good.basePrice + demand * 0.5,
        demand,
      }
      return inv
    }, {} as any),
  }))
}

// Create initial game state with player stats
export function createInitialGameState(): GameState {
  return {
    cities: initializeCityInventories(INITIAL_CITIES),
    fleets: INITIAL_FLEETS,
    npcFleets: [],
    lastNPCSpawnTick: 0,
    tradeRoutes: [],
    goods: GOODS,
    currentTick: 0,
    isRunning: false,
    tickInterval: CONFIG.GAME.TICK_INTERVAL_MS,
    player: {
      money: CONFIG.DIFFICULTY.STARTING_MONEY,
      totalProfit: 0,
      totalTrades: 0,
      reputation: CONFIG.DIFFICULTY.STARTING_REPUTATION,
      tradeHistory: [],
      activeInvestments: [],
      totalInvested: 0,
      investmentReturns: 0,
    },
  }
}
