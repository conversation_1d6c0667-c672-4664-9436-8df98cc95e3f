import type { GameState, City, PlayerInvestment, Industry } from "../types/game"
import { CONFIG } from "../config/config"
import { ProductionChainManager } from "./productionChainManager"

export class PlayerInvestmentManager {
  private gameState: GameState

  constructor(gameState: GameState) {
    this.gameState = gameState
  }

  // Get available investment opportunities for a city
  getAvailableInvestments(cityId: string): Array<{
    type: "industry" | "port_upgrade"
    id: string
    name: string
    description: string
    cost: number
    buildTime: number
    requirements: string[]
    canAfford: boolean
    canBuild: boolean
    expectedReturns: number
  }> {
    const city = this.gameState.cities.find((c) => c.id === cityId)
    if (!city) return []

    const opportunities = []

    // Check industry investments
    Object.values(CONFIG.PRODUCTION_CHAINS.RECIPES).forEach((recipe) => {
      // Skip if city already has this industry
      if (city.industries.some((ind) => ind.recipeId === recipe.id)) return

      // Check if player can build this industry
      if (ProductionChainManager.canBuildIndustry(city, recipe.id)) {
        const cost = this.calculateInvestmentCost(recipe.buildCost, "industry")
        const expectedReturns = this.calculateExpectedReturns(recipe, city)

        opportunities.push({
          type: "industry" as const,
          id: recipe.id,
          name: recipe.name,
          description: recipe.description,
          cost,
          buildTime: recipe.buildTime,
          requirements: this.getIndustryRequirements(recipe, city),
          canAfford: this.gameState.player.money >= cost,
          canBuild: this.canPlayerInvest("industry", recipe.id, city),
          expectedReturns,
        })
      }
    })

    // Check port upgrade investments
    Object.values(CONFIG.PORT_UPGRADES.AVAILABLE_UPGRADES).forEach((upgrade) => {
      // Skip if city already has this upgrade
      if (city.port.upgrades.includes(upgrade.id)) return

      // Check requirements
      const requirements =
        CONFIG.PORT_UPGRADES.UPGRADE_REQUIREMENTS[upgrade.id as keyof typeof CONFIG.PORT_UPGRADES.UPGRADE_REQUIREMENTS]
      if (requirements) {
        let canBuild = true
        const reqList = []

        if (city.population < requirements.populationMin) {
          canBuild = false
          reqList.push(`Population: ${requirements.populationMin}+ (current: ${city.population})`)
        }

        if (requirements.requires) {
          const missingUpgrades = requirements.requires.filter((req) => !city.port.upgrades.includes(req))
          if (missingUpgrades.length > 0) {
            canBuild = false
            reqList.push(`Requires: ${missingUpgrades.join(", ")}`)
          }
        }

        const cost = this.calculateInvestmentCost(upgrade.cost, "port_upgrade")
        const expectedReturns = this.calculatePortUpgradeReturns(upgrade, city)

        opportunities.push({
          type: "port_upgrade" as const,
          id: upgrade.id,
          name: upgrade.name,
          description: upgrade.description,
          cost,
          buildTime: upgrade.buildTime,
          requirements: reqList,
          canAfford: this.gameState.player.money >= cost,
          canBuild: canBuild && this.canPlayerInvest("port_upgrade", upgrade.id, city),
          expectedReturns,
        })
      }
    })

    return opportunities.sort((a, b) => b.expectedReturns - a.expectedReturns)
  }

  // Make an investment
  makeInvestment(cityId: string, type: "industry" | "port_upgrade", targetId: string): boolean {
    const city = this.gameState.cities.find((c) => c.id === cityId)
    if (!city) return false

    const opportunities = this.getAvailableInvestments(cityId)
    const opportunity = opportunities.find((opp) => opp.type === type && opp.id === targetId)

    if (!opportunity || !opportunity.canAfford || !opportunity.canBuild) return false

    // Deduct money
    this.gameState.player.money -= opportunity.cost
    this.gameState.player.totalInvested += opportunity.cost

    // Create investment record
    const investment: PlayerInvestment = {
      id: `${type}-${targetId}-${this.gameState.currentTick}`,
      type,
      cityId,
      targetId,
      investmentAmount: opportunity.cost,
      startTick: this.gameState.currentTick,
      completionTick: this.gameState.currentTick + opportunity.buildTime,
      status: "under_construction",
      expectedReturns: opportunity.expectedReturns,
    }

    this.gameState.player.activeInvestments.push(investment)

    // Start construction
    if (type === "industry") {
      this.startIndustryConstruction(city, targetId, investment)
    } else if (type === "port_upgrade") {
      this.startPortUpgradeConstruction(city, targetId, investment)
    }

    if (CONFIG.DEBUG.LOG_PLAYER_INVESTMENTS) {
      console.log(`Player invested $${opportunity.cost} in ${opportunity.name} at ${city.name}`)
    }

    return true
  }

  // Process ongoing investments
  processInvestments() {
    this.gameState.player.activeInvestments = this.gameState.player.activeInvestments.map((investment) => {
      if (investment.status === "under_construction" && investment.completionTick) {
        if (this.gameState.currentTick >= investment.completionTick) {
          this.completeInvestment(investment)
          return { ...investment, status: "completed" as const }
        }
      }
      return investment
    })

    // Calculate investment returns
    this.calculateInvestmentReturns()
  }

  // Complete an investment
  private completeInvestment(investment: PlayerInvestment) {
    const city = this.gameState.cities.find((c) => c.id === investment.cityId)
    if (!city) return

    if (investment.type === "industry") {
      // Find the industry and mark it as player-owned
      const industry = city.industries.find((ind) => ind.recipeId === investment.targetId && ind.isUnderConstruction)
      if (industry) {
        industry.isPlayerOwned = true
        industry.playerInvestment = investment.investmentAmount
        industry.playerProfitShare = CONFIG.PLAYER_INVESTMENT.INDUSTRY_PROFIT_SHARE
        industry.isUnderConstruction = false
        industry.isActive = true
      }
    } else if (investment.type === "port_upgrade") {
      // Complete port upgrade construction
      city.port.upgrades.push(investment.targetId)
      city.port.underConstruction = undefined
    }

    if (CONFIG.DEBUG.LOG_PLAYER_INVESTMENTS) {
      console.log(`Player investment completed: ${investment.targetId} at ${city.name}`)
    }
  }

  // Calculate returns from completed investments
  private calculateInvestmentReturns() {
    let totalReturns = 0

    this.gameState.cities.forEach((city) => {
      // Returns from player-owned industries
      city.industries
        .filter((industry) => industry.isPlayerOwned && industry.isActive)
        .forEach((industry) => {
          const recipe = ProductionChainManager.getRecipe(industry.recipeId)
          if (recipe) {
            // Calculate profit based on output value minus input costs and upkeep
            const outputValue = Object.entries(recipe.outputs).reduce((total, [goodId, amount]) => {
              const good = this.gameState.goods.find((g) => g.id === goodId)
              return total + (good ? good.basePrice * amount * industry.efficiency : 0)
            }, 0)

            const inputCost = Object.entries(recipe.inputs).reduce((total, [goodId, amount]) => {
              const good = this.gameState.goods.find((g) => g.id === goodId)
              return total + (good ? good.basePrice * amount : 0)
            }, 0)

            const profit = (outputValue - inputCost - recipe.upkeepCost) * industry.playerProfitShare
            if (profit > 0) {
              totalReturns += profit
            }
          }
        })

      // Returns from port upgrades (simplified - small bonus based on trade volume)
      if (city.port.upgrades.length > 0) {
        const tradeVolume = Object.values(city.inventory).reduce((sum, item) => sum + item.quantity * 0.01, 0)
        totalReturns += city.port.upgrades.length * tradeVolume * 0.1
      }
    })

    this.gameState.player.money += totalReturns
    this.gameState.player.investmentReturns += totalReturns
  }

  // Helper methods
  private calculateInvestmentCost(baseCost: number, type: "industry" | "port_upgrade"): number {
    let cost = baseCost

    // Apply reputation discount
    const reputation = this.gameState.player.reputation
    Object.entries(CONFIG.PLAYER_INVESTMENT.REPUTATION_DISCOUNT).forEach(([minRep, discount]) => {
      if (reputation >= Number.parseInt(minRep)) {
        cost *= discount
      }
    })

    return Math.round(cost)
  }

  private calculateExpectedReturns(recipe: any, city: City): number {
    // Simplified calculation based on output value and efficiency
    const outputValue = Object.entries(recipe.outputs).reduce((total, [goodId, amount]) => {
      const good = this.gameState.goods.find((g) => g.id === goodId)
      return total + (good ? good.basePrice * amount : 0)
    }, 0)

    const inputCost = Object.entries(recipe.inputs).reduce((total, [goodId, amount]) => {
      const good = this.gameState.goods.find((g) => g.id === goodId)
      return total + (good ? good.basePrice * amount : 0)
    }, 0)

    const profitPerTick = (outputValue - inputCost - recipe.upkeepCost) * CONFIG.PLAYER_INVESTMENT.INDUSTRY_PROFIT_SHARE
    return Math.max(0, profitPerTick)
  }

  private calculatePortUpgradeReturns(upgrade: any, city: City): number {
    // Simplified calculation based on trade volume improvement
    const tradeVolume = Object.values(city.inventory).reduce((sum, item) => sum + item.quantity * 0.01, 0)
    return tradeVolume * 0.05 // 5% of trade volume as expected returns
  }

  private canPlayerInvest(type: "industry" | "port_upgrade", targetId: string, city: City): boolean {
    // Check reputation requirement
    if (this.gameState.player.reputation < CONFIG.PLAYER_INVESTMENT.MIN_REPUTATION_FOR_INVESTMENT) {
      return false
    }

    // Check if already investing in this
    const existingInvestment = this.gameState.player.activeInvestments.find(
      (inv) => inv.cityId === city.id && inv.targetId === targetId && inv.status === "under_construction",
    )

    return !existingInvestment
  }

  private getIndustryRequirements(recipe: any, city: City): string[] {
    const requirements = []

    if (recipe.buildMaterials) {
      Object.entries(recipe.buildMaterials).forEach(([goodId, amount]) => {
        const available = city.inventory[goodId]?.quantity || 0
        const good = this.gameState.goods.find((g) => g.id === goodId)
        if (available < amount) {
          requirements.push(`${good?.name || goodId}: ${amount} (have ${available})`)
        }
      })
    }

    return requirements
  }

  private startIndustryConstruction(city: City, recipeId: string, investment: PlayerInvestment) {
    // Create the industry in construction state
    const recipe = ProductionChainManager.getRecipe(recipeId)
    if (!recipe) return

    const newIndustry: Industry = {
      id: `${city.id}-${recipeId}-player-${this.gameState.currentTick}`,
      recipeId,
      name: `${recipe.name} (Player Investment)`,
      efficiency: ProductionChainManager.calculateIndustryEfficiency({} as Industry, city),
      isActive: false,
      isUnderConstruction: true,
      constructionStartTick: this.gameState.currentTick,
      inputBuffer: {},
      outputBuffer: {},
      processingProgress: 0,
      lastProcessedTick: this.gameState.currentTick,
      isPlayerOwned: true,
      playerInvestment: investment.investmentAmount,
      playerProfitShare: CONFIG.PLAYER_INVESTMENT.INDUSTRY_PROFIT_SHARE,
    }

    city.industries.push(newIndustry)

    // Consume build materials if required
    if (recipe.buildMaterials) {
      Object.entries(recipe.buildMaterials).forEach(([goodId, amount]) => {
        if (city.inventory[goodId]) {
          city.inventory[goodId].quantity = Math.max(0, city.inventory[goodId].quantity - amount)
        }
      })
    }
  }

  private startPortUpgradeConstruction(city: City, upgradeId: string, investment: PlayerInvestment) {
    city.port.underConstruction = {
      upgradeId,
      startTick: this.gameState.currentTick,
      completionTick: investment.completionTick!,
    }
  }

  // Get investment summary for UI
  getInvestmentSummary() {
    const active = this.gameState.player.activeInvestments.filter((inv) => inv.status === "under_construction")
    const completed = this.gameState.player.activeInvestments.filter((inv) => inv.status === "completed")

    return {
      totalInvested: this.gameState.player.totalInvested,
      totalReturns: this.gameState.player.investmentReturns,
      activeInvestments: active.length,
      completedInvestments: completed.length,
      activeInvestmentsList: active,
      monthlyReturns: this.calculateMonthlyReturns(),
    }
  }

  private calculateMonthlyReturns(): number {
    // Calculate expected monthly returns from all completed investments
    let monthlyReturns = 0

    this.gameState.cities.forEach((city) => {
      city.industries
        .filter((industry) => industry.isPlayerOwned && industry.isActive)
        .forEach((industry) => {
          const recipe = ProductionChainManager.getRecipe(industry.recipeId)
          if (recipe) {
            const outputValue = Object.entries(recipe.outputs).reduce((total, [goodId, amount]) => {
              const good = this.gameState.goods.find((g) => g.id === goodId)
              return total + (good ? good.basePrice * amount * industry.efficiency : 0)
            }, 0)

            const inputCost = Object.entries(recipe.inputs).reduce((total, [goodId, amount]) => {
              const good = this.gameState.goods.find((g) => g.id === goodId)
              return total + (good ? good.basePrice * amount : 0)
            }, 0)

            const profit = (outputValue - inputCost - recipe.upkeepCost) * industry.playerProfitShare
            monthlyReturns += Math.max(0, profit * 30) // Approximate monthly (30 ticks)
          }
        })
    })

    return monthlyReturns
  }
}
