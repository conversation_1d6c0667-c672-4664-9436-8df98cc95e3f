import type { City } from "../types/game"
import { CONFIG } from "../config/config"

export class CityEconomicsManager {
  // Calculate population-based consumption for a city
  static calculatePopulationConsumption(city: City): { [goodId: string]: number } {
    const populationInThousands = city.population / 1000
    const wealthLevel = this.calculateCityWealthLevel(city)
    const wealthModifiers = CONFIG.POPULATION_CONSUMPTION.CITY_WEALTH_MODIFIERS[wealthLevel]

    const consumption: { [goodId: string]: number } = {}

    // Calculate consumption for each population good
    Object.entries(CONFIG.POPULATION_CONSUMPTION.BASE_CONSUMPTION_PER_1K_POPULATION).forEach(([goodId, baseRate]) => {
      // Base consumption scaled by population
      let consumptionAmount = baseRate * populationInThousands

      // Apply wealth modifier
      consumptionAmount *= wealthModifiers[goodId as keyof typeof wealthModifiers] || 1.0

      // Apply random variance
      const variance = 1 + (Math.random() - 0.5) * CONFIG.POPULATION_CONSUMPTION.CONSUMPTION_VARIANCE
      consumptionAmount *= variance

      // Apply shortage adaptation if enabled
      if (CONFIG.POPULATION_CONSUMPTION.SHORTAGE_ADAPTATION.ENABLED) {
        const inventory = city.inventory[goodId]
        if (inventory && inventory.quantity < CONFIG.POPULATION_CONSUMPTION.SHORTAGE_ADAPTATION.SHORTAGE_THRESHOLD) {
          consumptionAmount *= CONFIG.POPULATION_CONSUMPTION.SHORTAGE_ADAPTATION.SHORTAGE_CONSUMPTION_MULTIPLIER
        }
      }

      consumption[goodId] = Math.max(0, Math.round(consumptionAmount * 100) / 100) // Round to 2 decimal places
    })

    return consumption
  }

  // Calculate specialized consumption (tools, wood, iron, etc.)
  static calculateSpecializedConsumption(city: City): { [goodId: string]: number } {
    const populationInThousands = city.population / 1000
    const consumption: { [goodId: string]: number } = {}

    Object.entries(CONFIG.CITIES.SPECIALIZED_CONSUMPTION).forEach(([goodId, baseRate]) => {
      let consumptionAmount = baseRate * populationInThousands

      // Apply variance
      const variance = 1 + (Math.random() - 0.5) * CONFIG.CITIES.CONSUMPTION_VARIANCE
      consumptionAmount *= variance

      consumption[goodId] = Math.max(0, Math.round(consumptionAmount * 100) / 100)
    })

    return consumption
  }

  // Determine city wealth level based on production and population
  static calculateCityWealthLevel(city: City): "poor" | "normal" | "wealthy" {
    let wealthScore = 0

    // Base score from population (larger cities tend to be wealthier)
    wealthScore += city.population * CONFIG.POPULATION_CONSUMPTION.WEALTH_CALCULATION.POPULATION_WEALTH_FACTOR

    // Bonus for luxury good production
    const luxuryGoods = ["rum", "cloth", "sugar"]
    luxuryGoods.forEach((goodId) => {
      if (city.production[goodId]) {
        wealthScore +=
          city.production[goodId] * CONFIG.POPULATION_CONSUMPTION.WEALTH_CALCULATION.LUXURY_PRODUCTION_BONUS
      }
    })

    // Classify based on score
    if (wealthScore > 15) return "wealthy"
    if (wealthScore < 5) return "poor"
    return "normal"
  }

  // Process all consumption for a city (both population-based and specialized)
  static processConsumption(city: City): { [goodId: string]: number } {
    const populationConsumption = this.calculatePopulationConsumption(city)
    const specializedConsumption = this.calculateSpecializedConsumption(city)

    // Merge both types of consumption
    const totalConsumption: { [goodId: string]: number } = {}

    // Add population consumption
    Object.entries(populationConsumption).forEach(([goodId, amount]) => {
      totalConsumption[goodId] = (totalConsumption[goodId] || 0) + amount
    })

    // Add specialized consumption
    Object.entries(specializedConsumption).forEach(([goodId, amount]) => {
      totalConsumption[goodId] = (totalConsumption[goodId] || 0) + amount
    })

    return totalConsumption
  }

  // Get consumption breakdown for UI display
  static getConsumptionBreakdown(city: City): {
    population: { [goodId: string]: number }
    specialized: { [goodId: string]: number }
    total: { [goodId: string]: number }
    wealthLevel: "poor" | "normal" | "wealthy"
  } {
    const populationConsumption = this.calculatePopulationConsumption(city)
    const specializedConsumption = this.calculateSpecializedConsumption(city)
    const totalConsumption = this.processConsumption(city)
    const wealthLevel = this.calculateCityWealthLevel(city)

    return {
      population: populationConsumption,
      specialized: specializedConsumption,
      total: totalConsumption,
      wealthLevel,
    }
  }
}
