import type { City, Industry, ProductionRecipe } from "../types/game"
import { CONFIG } from "../config/config"

export class ProductionChainManager {
  // Get recipe by ID
  static getRecipe(recipeId: string): ProductionRecipe | undefined {
    return CONFIG.PRODUCTION_CHAINS.RECIPES[recipeId as keyof typeof CONFIG.PRODUCTION_CHAINS.RECIPES]
  }

  // Process all industries in a city
  static processIndustries(city: City, currentTick: number): City {
    const updatedCity = { ...city }

    // Process each industry
    updatedCity.industries = city.industries.map((industry) => {
      if (industry.isUnderConstruction) {
        return this.processConstruction(industry, currentTick)
      } else if (industry.isActive) {
        return this.processProduction(industry, city, currentTick)
      }
      return industry
    })

    // Distribute outputs to city inventory
    updatedCity.industries.forEach((industry) => {
      Object.entries(industry.outputBuffer).forEach(([goodId, quantity]) => {
        if (quantity > 0 && updatedCity.inventory[goodId]) {
          updatedCity.inventory[goodId].quantity += quantity
          industry.outputBuffer[goodId] = 0
        }
      })
    })

    return updatedCity
  }

  // Process construction of new industries
  private static processConstruction(industry: Industry, currentTick: number): Industry {
    const recipe = this.getRecipe(industry.recipeId)
    if (!recipe) return industry

    const constructionTime =
      CONFIG.PRODUCTION_CHAINS.INDUSTRY_DEVELOPMENT.CONSTRUCTION_TIME[
        recipe.category as keyof typeof CONFIG.PRODUCTION_CHAINS.INDUSTRY_DEVELOPMENT.CONSTRUCTION_TIME
      ] || 5

    if (industry.constructionStartTick && currentTick - industry.constructionStartTick >= constructionTime) {
      return {
        ...industry,
        isUnderConstruction: false,
        isActive: true,
        constructionStartTick: undefined,
      }
    }

    return industry
  }

  // Process production for an active industry
  private static processProduction(industry: Industry, city: City, currentTick: number): Industry {
    const recipe = this.getRecipe(industry.recipeId)
    if (!recipe) return industry

    // Check if we have enough inputs
    const hasInputs = Object.entries(recipe.inputs).every(([goodId, required]) => {
      const available = (industry.inputBuffer[goodId] || 0) + (city.inventory[goodId]?.quantity || 0)
      return available >= required
    })

    if (!hasInputs) {
      return { ...industry, isActive: false } // Shut down if no inputs
    }

    // Gather inputs from city inventory to input buffer
    Object.entries(recipe.inputs).forEach(([goodId, required]) => {
      const needed = required - (industry.inputBuffer[goodId] || 0)
      if (needed > 0 && city.inventory[goodId]) {
        const taken = Math.min(needed, city.inventory[goodId].quantity)
        city.inventory[goodId].quantity -= taken
        industry.inputBuffer[goodId] = (industry.inputBuffer[goodId] || 0) + taken
      }
    })

    // Process production
    if (recipe.processingTime <= 1) {
      // Instant processing
      return this.processInstantProduction(industry, recipe)
    } else {
      // Multi-tick processing
      return this.processTimedProduction(industry, recipe, currentTick)
    }
  }

  // Handle instant production (1 tick)
  private static processInstantProduction(industry: Industry, recipe: ProductionRecipe): Industry {
    // Check if we have enough inputs in buffer
    const canProduce = Object.entries(recipe.inputs).every(([goodId, required]) => {
      return (industry.inputBuffer[goodId] || 0) >= required
    })

    if (!canProduce) return industry

    // Consume inputs
    Object.entries(recipe.inputs).forEach(([goodId, required]) => {
      industry.inputBuffer[goodId] = (industry.inputBuffer[goodId] || 0) - required
    })

    // Apply efficiency and produce outputs
    const efficiency = industry.efficiency
    Object.entries(recipe.outputs).forEach(([goodId, baseAmount]) => {
      const actualAmount = baseAmount * efficiency
      industry.outputBuffer[goodId] = (industry.outputBuffer[goodId] || 0) + actualAmount
    })

    return industry
  }

  // Handle timed production (multiple ticks)
  private static processTimedProduction(industry: Industry, recipe: ProductionRecipe, currentTick: number): Industry {
    // Start processing if we have inputs and aren't already processing
    if (industry.processingProgress === 0) {
      const canStart = Object.entries(recipe.inputs).every(([goodId, required]) => {
        return (industry.inputBuffer[goodId] || 0) >= required
      })

      if (canStart) {
        // Consume inputs and start processing
        Object.entries(recipe.inputs).forEach(([goodId, required]) => {
          industry.inputBuffer[goodId] = (industry.inputBuffer[goodId] || 0) - required
        })
        industry.processingProgress = 1 / recipe.processingTime
        industry.lastProcessedTick = currentTick
      }
    } else {
      // Continue processing
      if (currentTick > industry.lastProcessedTick) {
        industry.processingProgress += 1 / recipe.processingTime
        industry.lastProcessedTick = currentTick

        // Check if processing is complete
        if (industry.processingProgress >= 1) {
          // Apply efficiency and produce outputs
          const efficiency = industry.efficiency
          Object.entries(recipe.outputs).forEach(([goodId, baseAmount]) => {
            const actualAmount = baseAmount * efficiency
            industry.outputBuffer[goodId] = (industry.outputBuffer[goodId] || 0) + actualAmount
          })

          industry.processingProgress = 0
        }
      }
    }

    return industry
  }

  // Calculate industry efficiency based on city conditions
  static calculateIndustryEfficiency(industry: Industry, city: City): number {
    const recipe = this.getRecipe(industry.recipeId)
    if (!recipe) return 0

    let efficiency = recipe.baseEfficiency

    // Apply wealth modifier
    const wealthLevel = this.getCityWealthLevel(city)
    efficiency *= CONFIG.PRODUCTION_CHAINS.EFFICIENCY_MODIFIERS.WEALTH_BONUS[wealthLevel]

    // Apply population modifier
    const populationLevel = this.getCityPopulationLevel(city)
    efficiency *= CONFIG.PRODUCTION_CHAINS.EFFICIENCY_MODIFIERS.POPULATION_BONUS[populationLevel]

    // Apply random variance
    const variance = 1 + (Math.random() - 0.5) * CONFIG.PRODUCTION_CHAINS.EFFICIENCY_MODIFIERS.RANDOM_VARIANCE
    efficiency *= variance

    return Math.max(0.1, Math.min(2.0, efficiency)) // Clamp between 10% and 200%
  }

  // Determine if a city can build a new industry
  static canBuildIndustry(city: City, recipeId: string): boolean {
    const recipe = this.getRecipe(recipeId)
    if (!recipe) return false

    const categoryReqs =
      CONFIG.PRODUCTION_CHAINS.INDUSTRY_CATEGORIES[
        recipe.category as keyof typeof CONFIG.PRODUCTION_CHAINS.INDUSTRY_CATEGORIES
      ]
    if (!categoryReqs) return false

    // Check population requirement
    if (city.population < categoryReqs.populationRequirement) return false

    // Check wealth requirement
    if (city.wealth < categoryReqs.wealthRequirement) return false

    // Check if city already has this industry
    if (city.industries.some((ind) => ind.recipeId === recipeId)) return false

    // Check input availability (simplified - just check if city produces or has access to inputs)
    const inputAvailability = Object.keys(recipe.inputs).reduce((available, goodId) => {
      const hasLocal = city.production[goodId] > 0 || (city.inventory[goodId]?.quantity || 0) > 20
      return available + (hasLocal ? 1 : 0)
    }, 0)

    const inputRequirement =
      Object.keys(recipe.inputs).length *
      CONFIG.PRODUCTION_CHAINS.INDUSTRY_DEVELOPMENT.BUILD_REQUIREMENTS.INPUT_AVAILABILITY

    return inputAvailability >= inputRequirement
  }

  // Build a new industry in a city
  static buildIndustry(city: City, recipeId: string, currentTick: number): City {
    if (!this.canBuildIndustry(city, recipeId)) return city

    const recipe = this.getRecipe(recipeId)
    if (!recipe) return city

    const newIndustry: Industry = {
      id: `${city.id}-${recipeId}-${currentTick}`,
      recipeId,
      name: recipe.name,
      efficiency: this.calculateIndustryEfficiency({} as Industry, city),
      isActive: false,
      isUnderConstruction: true,
      constructionStartTick: currentTick,
      inputBuffer: {},
      outputBuffer: {},
      processingProgress: 0,
      lastProcessedTick: currentTick,
    }

    return {
      ...city,
      industries: [...city.industries, newIndustry],
      wealth:
        city.wealth -
        (CONFIG.PRODUCTION_CHAINS.INDUSTRY_CATEGORIES[
          recipe.category as keyof typeof CONFIG.PRODUCTION_CHAINS.INDUSTRY_CATEGORIES
        ]?.wealthRequirement || 0),
    }
  }

  // Attempt to develop new industries in a city
  static attemptIndustryDevelopment(city: City, currentTick: number): City {
    if (Math.random() > CONFIG.PRODUCTION_CHAINS.INDUSTRY_DEVELOPMENT.DEVELOPMENT_CHANCE) {
      return city
    }

    // Get available recipes that the city can build
    const availableRecipes = Object.keys(CONFIG.PRODUCTION_CHAINS.RECIPES).filter((recipeId) =>
      this.canBuildIndustry(city, recipeId),
    )

    if (availableRecipes.length === 0) return city

    // Pick a random recipe to build
    const selectedRecipe = availableRecipes[Math.floor(Math.random() * availableRecipes.length)]

    if (CONFIG.DEBUG.LOG_PRODUCTION_CHAINS) {
      console.log(`${city.name} is building new industry: ${selectedRecipe}`)
    }

    return this.buildIndustry(city, selectedRecipe, currentTick)
  }

  // Helper methods
  private static getCityWealthLevel(city: City): "wealthy" | "normal" | "poor" {
    if (city.wealth > 20000) return "wealthy"
    if (city.wealth < 5000) return "poor"
    return "normal"
  }

  private static getCityPopulationLevel(city: City): "large" | "medium" | "small" {
    if (city.population >= 5000) return "large"
    if (city.population >= 2000) return "medium"
    return "small"
  }

  // Get industry status for UI
  static getIndustryStatus(city: City) {
    return {
      active: city.industries.filter((ind) => ind.isActive).length,
      underConstruction: city.industries.filter((ind) => ind.isUnderConstruction).length,
      total: city.industries.length,
      categories: city.industries.reduce(
        (acc, ind) => {
          const recipe = this.getRecipe(ind.recipeId)
          if (recipe) {
            acc[recipe.category] = (acc[recipe.category] || 0) + 1
          }
          return acc
        },
        {} as Record<string, number>,
      ),
    }
  }
}
