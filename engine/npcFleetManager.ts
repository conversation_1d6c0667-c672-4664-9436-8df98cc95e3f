import type { City, NPCFleet, MarketOpportunity, GameState, Fleet, Ship } from "../types/game"
import { CONFIG } from "../config/config"
import { selectRandomShipType, getShipType } from "../types/shipTypes"

export class NPCFleetManager {
  private gameState: GameState

  constructor(gameState: GameState) {
    this.gameState = gameState
  }

  // Main method called each tick
  processNPCFleets() {
    if (!CONFIG.NPC_FLEETS.ENABLED) return

    // Process existing NPC fleets
    this.updateNPCFleets()

    // Check for new trading opportunities
    this.checkForTradingOpportunities()

    // Clean up completed fleets
    this.cleanupCompletedFleets()
  }

  private updateNPCFleets() {
    this.gameState.npcFleets = this.gameState.npcFleets.map((fleet) => {
      const mission = fleet.mission

      // Update fleet properties based on ships
      this.updateFleetProperties(fleet)

      switch (mission.status) {
        case "traveling_to_destination":
          // Check if all ships have arrived
          const allShipsArrived = fleet.ships.every((ship) => ship.currentCity === mission.destinationCityId)
          if (allShipsArrived) {
            mission.status = "trading"
            this.executeNPCTrade(fleet, "sell")
          }
          break

        case "trading":
          mission.status = "mission_complete"
          mission.completionTick = this.gameState.currentTick
          break

        case "mission_complete":
          if (
            mission.completionTick &&
            this.gameState.currentTick - mission.completionTick >= CONFIG.NPC_FLEETS.DESPAWN_DELAY_TICKS
          ) {
            return { ...fleet, mission: { ...mission, status: "returning" as const } }
          }
          break
      }

      return fleet
    })
  }

  private updateFleetProperties(fleet: Fleet) {
    // Calculate total capacity
    fleet.totalCapacity = fleet.ships.reduce((total, ship) => total + ship.capacity, 0)

    // Calculate effective speed (slowest ship determines fleet speed)
    fleet.effectiveSpeed = Math.min(...fleet.ships.map((ship) => ship.speed))
  }

  private checkForTradingOpportunities() {
    if (this.gameState.currentTick - this.gameState.lastNPCSpawnTick < CONFIG.NPC_FLEETS.SPAWN_COOLDOWN_TICKS) {
      return
    }

    if (this.gameState.npcFleets.length >= CONFIG.NPC_FLEETS.MAX_ACTIVE_NPC_FLEETS) {
      return
    }

    const opportunities = this.findTradingOpportunities()

    if (opportunities.length > 0 && Math.random() < CONFIG.NPC_FLEETS.SPAWN_PROBABILITY) {
      const bestOpportunity = opportunities.sort((a, b) => b.profitPotential - a.profitPotential)[0]
      this.spawnNPCFleet(bestOpportunity)
      this.gameState.lastNPCSpawnTick = this.gameState.currentTick
    }
  }

  private findTradingOpportunities(): MarketOpportunity[] {
    const opportunities: MarketOpportunity[] = []

    for (const surplusCity of this.gameState.cities) {
      for (const [goodId, inventory] of Object.entries(surplusCity.inventory)) {
        if (inventory.quantity > CONFIG.NPC_FLEETS.SURPLUS_THRESHOLD) {
          for (const shortageCity of this.gameState.cities) {
            if (surplusCity.id === shortageCity.id) continue

            const shortageInventory = shortageCity.inventory[goodId]
            if (
              shortageInventory &&
              shortageInventory.quantity < CONFIG.NPC_FLEETS.SHORTAGE_THRESHOLD &&
              shortageInventory.price / inventory.price >= CONFIG.NPC_FLEETS.PRICE_DIFFERENCE_THRESHOLD
            ) {
              const distance = this.calculateDistance(surplusCity, shortageCity)
              if (distance <= CONFIG.NPC_FLEETS.MAX_TRAVEL_DISTANCE) {
                const tradableQuantity = Math.floor(
                  (inventory.quantity - CONFIG.NPC_FLEETS.SURPLUS_THRESHOLD) * CONFIG.NPC_FLEETS.CARGO_PERCENTAGE,
                )
                const profitPotential = tradableQuantity * (shortageInventory.price - inventory.price) - distance * 0.1

                opportunities.push({
                  surplusCity: surplusCity.id,
                  shortageCity: shortageCity.id,
                  goodId,
                  surplusQuantity: inventory.quantity,
                  shortageQuantity: shortageInventory.quantity,
                  priceDifference: shortageInventory.price / inventory.price,
                  profitPotential,
                  distance,
                })
              }
            }
          }
        }
      }
    }

    return opportunities.filter((op) => op.profitPotential > 0)
  }

  private spawnNPCFleet(opportunity: MarketOpportunity) {
    const fleetId = `npc-fleet-${this.gameState.currentTick}-${Math.random().toString(36).substr(2, 9)}`

    // Determine fleet size
    const fleetSize = this.selectFleetSize()

    // Generate ships for the fleet
    const ships = this.generateFleetShips(fleetId, fleetSize, opportunity.surplusCity)

    // Calculate fleet properties
    const totalCapacity = ships.reduce((sum, ship) => sum + ship.capacity, 0)
    const effectiveSpeed = Math.min(...ships.map((ship) => ship.speed))

    // Determine cargo amount based on total fleet capacity
    const tradingQuantity = Math.min(
      Math.floor(opportunity.surplusQuantity * CONFIG.NPC_FLEETS.CARGO_PERCENTAGE),
      Math.floor(totalCapacity * 0.8), // Don't fill fleet completely
    )

    // Distribute cargo among ships
    this.distributeCargo(ships, opportunity.goodId, tradingQuantity)

    const npcFleet: NPCFleet = {
      id: fleetId,
      name: this.generateFleetName(ships),
      owner: "npc",
      isNPC: true,
      ships,
      totalCapacity,
      effectiveSpeed,
      mission: {
        originCityId: opportunity.surplusCity,
        destinationCityId: opportunity.shortageCity,
        goodId: opportunity.goodId,
        targetQuantity: tradingQuantity,
        expectedProfit: opportunity.profitPotential,
        status: "traveling_to_destination",
      },
    }

    // Set all ships to travel to destination
    npcFleet.ships.forEach((ship) => {
      ship.destination = opportunity.shortageCity
      ship.progress = 0
    })

    // Remove goods from surplus city
    const surplusCity = this.gameState.cities.find((c) => c.id === opportunity.surplusCity)
    if (surplusCity && surplusCity.inventory[opportunity.goodId]) {
      surplusCity.inventory[opportunity.goodId].quantity -= tradingQuantity
      surplusCity.inventory[opportunity.goodId].demand += tradingQuantity * 0.05
    }

    this.gameState.npcFleets.push(npcFleet)

    if (CONFIG.DEBUG.LOG_TRADE_TRANSACTIONS) {
      console.log(
        `NPC Fleet spawned: ${npcFleet.name} (${ships.length} ships) carrying ${tradingQuantity} ${opportunity.goodId} from ${opportunity.surplusCity} to ${opportunity.shortageCity}`,
      )
    }
  }

  private selectFleetSize(): number {
    const weights = CONFIG.NPC_FLEETS.FLEET_SIZE_WEIGHTS
    const random = Math.random()
    let cumulative = 0

    for (let i = 0; i < weights.length; i++) {
      cumulative += weights[i]
      if (random <= cumulative) {
        return i + 1 // +1 because array is 0-indexed but fleet size starts at 1
      }
    }

    return 1 // Fallback
  }

  private generateFleetShips(fleetId: string, count: number, originCity: string): Ship[] {
    const ships: Ship[] = []

    for (let i = 0; i < count; i++) {
      const shipType = selectRandomShipType()
      const shipId = `${fleetId}-ship-${i + 1}`

      // Apply some variance to ship properties
      const speedVariance = 1 + (Math.random() - 0.5) * CONFIG.NPC_FLEETS.SPEED_VARIANCE

      ships.push({
        id: shipId,
        name: this.generateShipName(shipType.name, i + 1),
        typeId: shipType.id,
        capacity: shipType.cargoCapacity,
        speed: Math.round(shipType.speed * speedVariance),
        cargo: {},
        currentCity: originCity,
        progress: 0,
      })
    }

    return ships
  }

  private distributeCargo(ships: Ship[], goodId: string, totalQuantity: number) {
    const totalCapacity = ships.reduce((sum, ship) => sum + ship.capacity, 0)

    ships.forEach((ship) => {
      // Distribute cargo proportionally to ship capacity
      const shipPortion = (ship.capacity / totalCapacity) * totalQuantity
      ship.cargo[goodId] = Math.floor(shipPortion)
    })

    // Handle any remaining cargo due to rounding
    let remaining = totalQuantity - ships.reduce((sum, ship) => sum + (ship.cargo[goodId] || 0), 0)
    let shipIndex = 0

    while (remaining > 0 && shipIndex < ships.length) {
      ships[shipIndex].cargo[goodId] = (ships[shipIndex].cargo[goodId] || 0) + 1
      remaining--
      shipIndex++
    }
  }

  private generateFleetName(ships: Ship[]): string {
    const adjectives = ["Swift", "Golden", "Royal", "Merchant", "Trading", "Caribbean", "Colonial", "Fortune"]
    const nouns = ["Company", "Traders", "Fleet", "Convoy", "Expedition", "Venture", "Enterprise"]

    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)]
    const noun = nouns[Math.floor(Math.random() * nouns.length)]

    if (ships.length === 1) {
      const shipType = getShipType(ships[0].typeId)
      return `${adjective} ${shipType?.name || "Vessel"}`
    } else {
      return `${adjective} ${noun}`
    }
  }

  private generateShipName(shipTypeName: string, index: number): string {
    const prefixes = ["HMS", "SS", "MV", ""]
    const names = [
      "Endeavour",
      "Victory",
      "Fortune",
      "Liberty",
      "Providence",
      "Prosperity",
      "Adventure",
      "Discovery",
      "Triumph",
      "Glory",
      "Hope",
      "Courage",
      "Defiance",
      "Intrepid",
      "Valiant",
      "Resolute",
      "Stalwart",
      "Dauntless",
    ]

    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
    const name = names[Math.floor(Math.random() * names.length)]
    const suffix = index > 1 ? ` ${index}` : ""

    return `${prefix} ${name}${suffix}`.trim()
  }

  private executeNPCTrade(fleet: NPCFleet, action: "buy" | "sell") {
    const mission = fleet.mission
    const city = this.gameState.cities.find((c) => c.id === fleet.ships[0].currentCity)

    if (!city || !city.inventory[mission.goodId]) return

    const cityInventory = city.inventory[mission.goodId]
    let totalSold = 0

    // Process each ship's cargo
    fleet.ships.forEach((ship) => {
      const cargoQuantity = ship.cargo[mission.goodId] || 0
      if (cargoQuantity === 0) return

      const sellQuantity = Math.floor(cargoQuantity * CONFIG.NPC_FLEETS.UNLOAD_PERCENTAGE)

      // Sell goods to city
      cityInventory.quantity += sellQuantity
      totalSold += sellQuantity

      // Remove from ship cargo
      ship.cargo[mission.goodId] = cargoQuantity - sellQuantity
    })

    // Update city demand
    cityInventory.demand -= totalSold * 0.1

    if (CONFIG.DEBUG.LOG_TRADE_TRANSACTIONS) {
      console.log(
        `NPC ${fleet.name} sold ${totalSold} ${mission.goodId} to ${city.name} for $${cityInventory.price * totalSold}`,
      )
    }
  }

  private cleanupCompletedFleets() {
    this.gameState.npcFleets = this.gameState.npcFleets.filter((fleet) => fleet.mission.status !== "returning")
  }

  private calculateDistance(city1: City, city2: City): number {
    const dx = city1.position.x - city2.position.x
    const dy = city1.position.y - city2.position.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  getNPCFleetStats() {
    return {
      activeFleets: this.gameState.npcFleets.length,
      maxFleets: CONFIG.NPC_FLEETS.MAX_ACTIVE_NPC_FLEETS,
      totalShips: this.gameState.npcFleets.reduce((sum, fleet) => sum + fleet.ships.length, 0),
      fleetsByStatus: this.gameState.npcFleets.reduce(
        (acc, fleet) => {
          acc[fleet.mission.status] = (acc[fleet.mission.status] || 0) + 1
          return acc
        },
        {} as Record<string, number>,
      ),
      shipTypeDistribution: this.getShipTypeDistribution(),
    }
  }

  private getShipTypeDistribution() {
    const distribution: Record<string, number> = {}

    this.gameState.npcFleets.forEach((fleet) => {
      fleet.ships.forEach((ship) => {
        distribution[ship.typeId] = (distribution[ship.typeId] || 0) + 1
      })
    })

    return distribution
  }
}
