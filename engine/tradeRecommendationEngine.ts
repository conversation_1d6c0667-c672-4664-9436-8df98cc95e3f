import type { GameState, City, Ship } from "../types/game"

export interface TradeRecommendation {
  goodId: string
  action: "buy" | "sell"
  quantity: number
  currentPrice: number
  targetCityId: string
  targetPrice: number
  profitPotential: number
  confidence: "high" | "medium" | "low"
  distance: number
}

export class TradeRecommendationEngine {
  static generateRecommendations(ship: Ship, currentCity: City, gameState: GameState): TradeRecommendation[] {
    const recommendations: TradeRecommendation[] = []

    // Analyze buy opportunities (goods to purchase in current city)
    Object.entries(currentCity.inventory).forEach(([goodId, cityItem]) => {
      if (cityItem.quantity > 10) {
        // Only recommend if city has reasonable stock
        const buyRecommendation = this.analyzeBuyOpportunity(goodId, currentCity, ship, gameState)
        if (buyRecommendation) {
          recommendations.push(buyRecommendation)
        }
      }
    })

    // Analyze sell opportunities (goods currently in ship cargo)
    Object.entries(ship.cargo).forEach(([goodId, quantity]) => {
      if (quantity > 0) {
        const sellRecommendation = this.analyzeSellOpportunity(goodId, currentCity, ship, gameState)
        if (sellRecommendation) {
          recommendations.push(sellRecommendation)
        }
      }
    })

    // Sort by profit potential
    return recommendations.sort((a, b) => b.profitPotential - a.profitPotential).slice(0, 10) // Return top 10 recommendations
  }

  private static analyzeBuyOpportunity(
    goodId: string,
    currentCity: City,
    ship: Ship,
    gameState: GameState,
  ): TradeRecommendation | null {
    const currentItem = currentCity.inventory[goodId]
    if (!currentItem) return null

    let bestOpportunity: TradeRecommendation | null = null
    let highestProfit = 0

    // Check all other cities for selling opportunities
    gameState.cities.forEach((targetCity) => {
      if (targetCity.id === currentCity.id) return

      const targetItem = targetCity.inventory[goodId]
      if (!targetItem) return

      const priceDifference = targetItem.price - currentItem.price
      if (priceDifference <= 0) return // No profit potential

      const distance = this.calculateDistance(currentCity, targetCity)
      const maxQuantity = Math.min(
        currentItem.quantity,
        ship.capacity - Object.values(ship.cargo).reduce((sum, qty) => sum + qty, 0),
        50, // Reasonable trade size
      )

      if (maxQuantity <= 0) return

      const grossProfit = priceDifference * maxQuantity
      const travelCost = distance * 0.1 // Simple travel cost calculation
      const netProfit = grossProfit - travelCost

      if (netProfit > highestProfit) {
        highestProfit = netProfit
        bestOpportunity = {
          goodId,
          action: "buy",
          quantity: maxQuantity,
          currentPrice: currentItem.price,
          targetCityId: targetCity.id,
          targetPrice: targetItem.price,
          profitPotential: netProfit,
          confidence: this.calculateConfidence(priceDifference, currentItem.quantity, targetItem.demand),
          distance,
        }
      }
    })

    return bestOpportunity
  }

  private static analyzeSellOpportunity(
    goodId: string,
    currentCity: City,
    ship: Ship,
    gameState: GameState,
  ): TradeRecommendation | null {
    const cargoQuantity = ship.cargo[goodId] || 0
    if (cargoQuantity === 0) return null

    const currentItem = currentCity.inventory[goodId]
    if (!currentItem) return null

    // Find the good's base price for comparison
    const good = gameState.goods.find((g) => g.id === goodId)
    if (!good) return null

    const profitPerUnit = currentItem.price - good.basePrice
    const totalProfit = profitPerUnit * cargoQuantity

    if (totalProfit <= 0) return null

    return {
      goodId,
      action: "sell",
      quantity: cargoQuantity,
      currentPrice: currentItem.price,
      targetCityId: currentCity.id,
      targetPrice: currentItem.price,
      profitPotential: totalProfit,
      confidence: this.calculateSellConfidence(currentItem.price, good.basePrice, currentItem.demand),
      distance: 0,
    }
  }

  private static calculateDistance(city1: City, city2: City): number {
    const dx = city1.position.x - city2.position.x
    const dy = city1.position.y - city2.position.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  private static calculateConfidence(
    priceDifference: number,
    supply: number,
    demand: number,
  ): "high" | "medium" | "low" {
    const priceRatio = priceDifference / 10 // Normalize price difference
    const supplyScore = Math.min(supply / 50, 1) // Good if supply > 50
    const demandScore = Math.max(demand / 20, 0) // Good if high demand

    const confidenceScore = (priceRatio + supplyScore + demandScore) / 3

    if (confidenceScore > 0.7) return "high"
    if (confidenceScore > 0.4) return "medium"
    return "low"
  }

  private static calculateSellConfidence(
    currentPrice: number,
    basePrice: number,
    demand: number,
  ): "high" | "medium" | "low" {
    const priceRatio = currentPrice / basePrice
    const demandScore = Math.max(demand / 20, 0)

    const confidenceScore = (priceRatio + demandScore) / 2

    if (confidenceScore > 1.5) return "high"
    if (confidenceScore > 1.2) return "medium"
    return "low"
  }

  // Get market analysis for a specific good across all cities
  static getMarketAnalysis(goodId: string, gameState: GameState) {
    const analysis = gameState.cities
      .map((city) => {
        const item = city.inventory[goodId]
        return {
          cityId: city.id,
          cityName: city.name,
          price: item?.price || 0,
          quantity: item?.quantity || 0,
          demand: item?.demand || 0,
        }
      })
      .filter((item) => item.price > 0)

    const prices = analysis.map((item) => item.price)
    const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length
    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)

    return {
      analysis,
      avgPrice,
      minPrice,
      maxPrice,
      priceSpread: maxPrice - minPrice,
    }
  }
}
