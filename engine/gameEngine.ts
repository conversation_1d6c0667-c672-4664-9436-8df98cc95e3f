import type { GameState, TradeTransaction } from "../types/game"
// Update imports to use the new constants
import { TIME, ECONOMY, PRODUCTION, DEBUG } from "../constants/gameConstants"
import { NPCFleetManager } from "./npcFleetManager"
import { CityEconomicsManager } from "./cityEconomicsManager"
import { ProductionChainManager } from "./productionChainManager"
import { PlayerInvestmentManager } from "./playerInvestmentManager"
import { TradeRecommendationEngine, type TradeRecommendation } from "./tradeRecommendationEngine"

export class GameEngine {
  private gameState: GameState
  private tickTimer: NodeJS.Timeout | null = null
  private onStateChange: (state: GameState) => void
  private npcFleetManager: NPCFleetManager
  private playerInvestmentManager: PlayerInvestmentManager

  constructor(initialState: GameState, onStateChange: (state: GameState) => void) {
    this.gameState = {
      ...initialState,
      tickInterval: TIME.DEFAULT_TICK_INTERVAL_MS,
      npcFleets: [],
      lastNPCSpawnTick: 0,
      player: {
        money: ECONOMY.STARTING_MONEY,
        totalProfit: 0,
        totalTrades: 0,
        reputation: ECONOMY.STARTING_REPUTATION,
        tradeHistory: [],
        activeInvestments: [],
        totalInvested: 0,
        investmentReturns: 0,
        ...initialState.player,
      },
    }
    this.onStateChange = onStateChange
    this.npcFleetManager = new NPCFleetManager(this.gameState)
    this.playerInvestmentManager = new PlayerInvestmentManager(this.gameState)
  }

  start() {
    if (this.gameState.isRunning) return

    this.gameState.isRunning = true
    this.tickTimer = setInterval(() => {
      this.processTick()
    }, this.gameState.tickInterval)

    this.onStateChange({ ...this.gameState })
  }

  stop() {
    if (!this.gameState.isRunning) return

    this.gameState.isRunning = false
    if (this.tickTimer) {
      clearInterval(this.tickTimer)
      this.tickTimer = null
    }

    this.onStateChange({ ...this.gameState })
  }

  private processTick() {
    this.gameState.currentTick++

    // Process city production chains and consumption
    this.processCityEconomics()

    // Move ships and process trades
    this.processFleetMovement()

    // Process NPC fleets
    this.npcFleetManager.processNPCFleets()

    // Process player investments
    this.playerInvestmentManager.processInvestments()

    // Update prices based on supply/demand
    this.updatePrices()

    this.onStateChange({ ...this.gameState })
  }

  private processCityEconomics() {
    this.gameState.cities = this.gameState.cities.map((city) => {
      let updatedCity = { ...city }

      // Process production chains
      updatedCity = ProductionChainManager.processIndustries(updatedCity, this.gameState.currentTick)

      // Update industry efficiencies (including player ownership bonus)
      updatedCity.industries = updatedCity.industries.map((industry) => {
        let efficiency = ProductionChainManager.calculateIndustryEfficiency(industry, updatedCity)

        // Apply player ownership bonus
        if (industry.isPlayerOwned) {
          efficiency *= PRODUCTION.EFFICIENCY_MODIFIERS.PLAYER_OWNERSHIP_BONUS
        }

        return {
          ...industry,
          efficiency,
        }
      })

      // Process legacy production (will be phased out)
      Object.entries(updatedCity.production).forEach(([goodId, amount]) => {
        if (updatedCity.inventory[goodId]) {
          // Apply production variance
          const variance = 1 + (Math.random() - 0.5) * PRODUCTION.PRODUCTION_VARIANCE
          const actualProduction = amount * variance
          updatedCity.inventory[goodId].quantity += actualProduction
        }
      })

      // Process consumption using existing system
      const totalConsumption = CityEconomicsManager.processConsumption(updatedCity)

      Object.entries(totalConsumption).forEach(([goodId, amount]) => {
        if (updatedCity.inventory[goodId]) {
          const previousQuantity = updatedCity.inventory[goodId].quantity
          updatedCity.inventory[goodId].quantity = Math.max(0, updatedCity.inventory[goodId].quantity - amount)

          // Update demand based on shortage
          const actualConsumption = previousQuantity - updatedCity.inventory[goodId].quantity
          const shortage = amount - actualConsumption
          if (shortage > 0) {
            // Increase demand when we can't consume as much as we want
            updatedCity.inventory[goodId].demand += shortage * ECONOMY.DEMAND_SHORTAGE_MULTIPLIER
          }
        }
      })

      // Attempt industry development
      updatedCity = ProductionChainManager.attemptIndustryDevelopment(updatedCity, this.gameState.currentTick)

      // Accumulate wealth from trade and production
      updatedCity.wealth += Math.random() * 100 // Simple wealth accumulation

      return updatedCity
    })
  }

  private processFleetMovement() {
    // Process player fleets
    this.gameState.fleets = this.gameState.fleets.map((fleet) => ({
      ...fleet,
      ships: fleet.ships.map((ship) => {
        if (ship.destination && ship.currentCity !== ship.destination) {
          const effectiveSpeed = ship.speed * (DEBUG.INSTANT_TRAVEL ? 100 : 1)
          ship.progress += effectiveSpeed / 100

          if (ship.progress >= 1) {
            ship.currentCity = ship.destination
            ship.destination = undefined
            ship.progress = 0
          }
        }
        return { ...ship }
      }),
    }))

    // Process NPC fleets
    this.gameState.npcFleets = this.gameState.npcFleets.map((fleet) => ({
      ...fleet,
      ships: fleet.ships.map((ship) => {
        if (ship.destination && ship.currentCity !== ship.destination) {
          const effectiveSpeed = ship.speed * (DEBUG.INSTANT_TRAVEL ? 100 : 1)
          ship.progress += effectiveSpeed / 100

          if (ship.progress >= 1) {
            ship.currentCity = ship.destination
            ship.destination = undefined
            ship.progress = 0
          }
        }
        return { ...ship }
      }),
    }))
  }

  private updatePrices() {
    this.gameState.cities = this.gameState.cities.map((city) => {
      const newInventory = { ...city.inventory }

      Object.entries(newInventory).forEach(([goodId, item]) => {
        const good = this.gameState.goods.find((g) => g.id === goodId)
        if (!good) return

        // Price based on supply/demand using config values
        const supplyFactor = Math.max(ECONOMY.PRICE_MIN_MULTIPLIER, item.quantity / ECONOMY.PRICE_SUPPLY_FACTOR)
        const demandFactor = Math.max(ECONOMY.PRICE_MIN_MULTIPLIER, (item.demand + 50) / ECONOMY.PRICE_DEMAND_FACTOR)

        const newPrice = good.basePrice * (demandFactor / supplyFactor)
        newInventory[goodId].price = Math.round(
          Math.min(
            good.basePrice * ECONOMY.PRICE_MAX_MULTIPLIER,
            Math.max(good.basePrice * ECONOMY.PRICE_MIN_MULTIPLIER, newPrice),
          ),
        )

        // Gradually reduce demand over time using config
        newInventory[goodId].demand *= ECONOMY.DEMAND_DECAY_RATE
      })

      return { ...city, inventory: newInventory }
    })
  }

  // Public methods for player actions
  moveShip(shipId: string, destinationCityId: string) {
    this.gameState.fleets = this.gameState.fleets.map((fleet) => ({
      ...fleet,
      ships: fleet.ships.map((ship) => {
        if (ship.id === shipId) {
          return { ...ship, destination: destinationCityId, progress: 0 }
        }
        return ship
      }),
    }))
    this.onStateChange({ ...this.gameState })
  }

  // Enhanced trading method with player money system
  tradeGood(shipId: string, cityId: string, goodId: string, quantity: number, action: "buy" | "sell"): boolean {
    const city = this.gameState.cities.find((c) => c.id === cityId)
    const ship = this.gameState.fleets.flatMap((f) => f.ships).find((s) => s.id === shipId)

    if (!city || !ship || ship.currentCity !== cityId) return false

    const cityItem = city.inventory[goodId]
    if (!cityItem) return false

    // Apply reputation modifier to prices
    const reputationModifier = 1 + (this.gameState.player.reputation - 50) * 0.002 // ±10% based on reputation

    if (action === "buy") {
      const canBuy = Math.min(
        quantity,
        cityItem.quantity,
        ship.capacity - Object.values(ship.cargo).reduce((sum, qty) => sum + qty, 0),
      )
      if (canBuy <= 0) return false

      const effectivePrice = Math.round(cityItem.price * reputationModifier)
      const totalCost = canBuy * effectivePrice

      if (this.gameState.player.money < totalCost) return false

      // Execute trade
      this.gameState.player.money -= totalCost
      cityItem.quantity -= canBuy
      cityItem.demand += canBuy * 0.1
      ship.cargo[goodId] = (ship.cargo[goodId] || 0) + canBuy

      // Record transaction
      const transaction: TradeTransaction = {
        id: `${this.gameState.currentTick}-${shipId}-${goodId}-buy`,
        tick: this.gameState.currentTick,
        shipId,
        cityId,
        goodId,
        quantity: canBuy,
        pricePerUnit: effectivePrice,
        totalValue: totalCost,
        action: "buy",
      }

      this.gameState.player.tradeHistory.push(transaction)
      this.gameState.player.totalTrades++

      // Update reputation based on trade size
      this.gameState.player.reputation += Math.min(0.1, totalCost / 10000)
    } else if (action === "sell") {
      const canSell = Math.min(quantity, ship.cargo[goodId] || 0)
      if (canSell <= 0) return false

      const effectivePrice = Math.round(cityItem.price * (2 - reputationModifier))
      const totalRevenue = canSell * effectivePrice

      // Calculate profit (need to find original purchase price)
      const good = this.gameState.goods.find((g) => g.id === goodId)
      const estimatedProfit = totalRevenue - (good ? canSell * good.basePrice : 0)

      // Execute trade
      this.gameState.player.money += totalRevenue
      this.gameState.player.totalProfit += estimatedProfit
      cityItem.quantity += canSell
      cityItem.demand -= canSell * 0.1
      ship.cargo[goodId] = Math.max(0, (ship.cargo[goodId] || 0) - canSell)

      // Record transaction
      const transaction: TradeTransaction = {
        id: `${this.gameState.currentTick}-${shipId}-${goodId}-sell`,
        tick: this.gameState.currentTick,
        shipId,
        cityId,
        goodId,
        quantity: canSell,
        pricePerUnit: effectivePrice,
        totalValue: totalRevenue,
        action: "sell",
        profit: estimatedProfit,
      }

      this.gameState.player.tradeHistory.push(transaction)
      this.gameState.player.totalTrades++

      // Update reputation based on trade size
      this.gameState.player.reputation += Math.min(0.1, totalRevenue / 10000)
    }

    // Keep trade history manageable
    if (this.gameState.player.tradeHistory.length > 100) {
      this.gameState.player.tradeHistory = this.gameState.player.tradeHistory.slice(-50)
    }

    // Clamp reputation between 0 and 100
    this.gameState.player.reputation = Math.max(0, Math.min(100, this.gameState.player.reputation))

    this.onStateChange({ ...this.gameState })
    return true
  }

  // Investment methods
  getAvailableInvestments(cityId: string) {
    return this.playerInvestmentManager.getAvailableInvestments(cityId)
  }

  makeInvestment(cityId: string, type: "industry" | "port_upgrade", targetId: string): boolean {
    const success = this.playerInvestmentManager.makeInvestment(cityId, type, targetId)
    if (success) {
      this.onStateChange({ ...this.gameState })
    }
    return success
  }

  getInvestmentSummary() {
    return this.playerInvestmentManager.getInvestmentSummary()
  }

  getGameState(): GameState {
    return { ...this.gameState }
  }

  getNPCFleetStats() {
    return this.npcFleetManager.getNPCFleetStats()
  }

  // Get city consumption breakdown for UI
  getCityConsumptionBreakdown(cityId: string) {
    const city = this.gameState.cities.find((c) => c.id === cityId)
    if (!city) return null

    return CityEconomicsManager.getConsumptionBreakdown(city)
  }

  // Get city industry status for UI
  getCityIndustryStatus(cityId: string) {
    const city = this.gameState.cities.find((c) => c.id === cityId)
    if (!city) return null

    return ProductionChainManager.getIndustryStatus(city)
  }

  // Get detailed industry information for UI
  getCityIndustries(cityId: string) {
    const city = this.gameState.cities.find((c) => c.id === cityId)
    if (!city) return []

    return city.industries.map((industry) => {
      const recipe = ProductionChainManager.getRecipe(industry.recipeId)
      return {
        ...industry,
        recipe,
      }
    })
  }

  // Get trade recommendations for a ship at a city
  getTradeRecommendations(shipId: string): TradeRecommendation[] {
    const ship = this.gameState.fleets.flatMap((f) => f.ships).find((s) => s.id === shipId)
    if (!ship || !ship.currentCity) return []

    const city = this.gameState.cities.find((c) => c.id === ship.currentCity)
    if (!city) return []

    return TradeRecommendationEngine.generateRecommendations(ship, city, this.gameState)
  }

  // Get market analysis for a specific good
  getMarketAnalysis(goodId: string) {
    return TradeRecommendationEngine.getMarketAnalysis(goodId, this.gameState)
  }

  // Get player statistics
  getPlayerStats() {
    const recentTrades = this.gameState.player.tradeHistory.slice(-10)
    const recentProfit = recentTrades.filter((t) => t.action === "sell").reduce((sum, t) => sum + (t.profit || 0), 0)

    return {
      ...this.gameState.player,
      recentProfit,
      recentTrades: recentTrades.length,
      averageTradeValue:
        this.gameState.player.totalTrades > 0
          ? this.gameState.player.tradeHistory.reduce((sum, t) => sum + t.totalValue, 0) /
            this.gameState.player.totalTrades
          : 0,
    }
  }
}
