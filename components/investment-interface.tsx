"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Building2,
  DollarSign,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Anchor,
  Factory,
  Hammer,
  Crown,
} from "lucide-react"
import type { City, PlayerStats } from "../types/game"
import { TooltipProvider } from "@/components/ui/tooltip"
import { InfoTooltip } from "./info-tooltip"

interface InvestmentOpportunity {
  type: "industry" | "port_upgrade"
  id: string
  name: string
  description: string
  cost: number
  buildTime: number
  requirements: string[]
  canAfford: boolean
  canBuild: boolean
  expectedReturns: number
}

interface InvestmentSummary {
  totalInvested: number
  totalReturns: number
  activeInvestments: number
  completedInvestments: number
  activeInvestmentsList: any[]
  monthlyReturns: number
}

interface InvestmentInterfaceProps {
  city: City
  player: PlayerStats
  opportunities: InvestmentOpportunity[]
  investmentSummary: InvestmentSummary
  onMakeInvestment: (type: "industry" | "port_upgrade", targetId: string) => void
}

export function InvestmentInterface({
  city,
  player,
  opportunities,
  investmentSummary,
  onMakeInvestment,
}: InvestmentInterfaceProps) {
  const [selectedCategory, setSelectedCategory] = useState<"all" | "industry" | "port_upgrade">("all")

  const filteredOpportunities = opportunities.filter((opp) => {
    if (selectedCategory === "all") return true
    return opp.type === selectedCategory
  })

  const industryOpportunities = opportunities.filter((opp) => opp.type === "industry")
  const portOpportunities = opportunities.filter((opp) => opp.type === "port_upgrade")

  const getTypeIcon = (type: "industry" | "port_upgrade") => {
    return type === "industry" ? <Factory className="w-4 h-4" /> : <Anchor className="w-4 h-4" />
  }

  const getStatusIcon = (opportunity: InvestmentOpportunity) => {
    if (!opportunity.canBuild) return <AlertCircle className="w-4 h-4 text-red-500" />
    if (!opportunity.canAfford) return <DollarSign className="w-4 h-4 text-yellow-500" />
    return <CheckCircle className="w-4 h-4 text-green-500" />
  }

  const getStatusText = (opportunity: InvestmentOpportunity) => {
    if (!opportunity.canBuild) return "Requirements not met"
    if (!opportunity.canAfford) return "Cannot afford"
    return "Ready to invest"
  }

  const getStatusColor = (opportunity: InvestmentOpportunity) => {
    if (!opportunity.canBuild) return "text-red-600"
    if (!opportunity.canAfford) return "text-yellow-600"
    return "text-green-600"
  }

  const canInvest = player.reputation >= 60 // CONFIG.PLAYER_INVESTMENT.MIN_REPUTATION_FOR_INVESTMENT

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Investment Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="w-5 h-5 text-yellow-600" />
              Investment Portfolio
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Total Invested:</span>
                <div className="font-semibold text-blue-600">${investmentSummary.totalInvested.toLocaleString()}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Total Returns:</span>
                <div className="font-semibold text-green-600">${investmentSummary.totalReturns.toLocaleString()}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Active Investments:</span>
                <div className="font-semibold">{investmentSummary.activeInvestments}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Monthly Returns:</span>
                <div className="font-semibold text-green-600">
                  ${investmentSummary.monthlyReturns.toLocaleString()}/month
                </div>
              </div>
            </div>

            {investmentSummary.activeInvestmentsList.length > 0 && (
              <div className="mt-4">
                <h4 className="font-semibold mb-2">Active Investments</h4>
                <div className="space-y-2">
                  {investmentSummary.activeInvestmentsList.map((investment) => {
                    const progress = investment.completionTick
                      ? Math.min(
                          100,
                          ((Date.now() - investment.startTick) / (investment.completionTick - investment.startTick)) *
                            100,
                        )
                      : 0

                    return (
                      <div key={investment.id} className="p-2 bg-blue-50 rounded border">
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-medium text-sm">{investment.targetId}</span>
                          <Badge variant="outline" className="text-xs">
                            <Clock className="w-3 h-3 mr-1" />
                            Building
                          </Badge>
                        </div>
                        <div className="flex justify-between text-xs text-muted-foreground mb-1">
                          <span>${investment.investmentAmount.toLocaleString()}</span>
                          <span>Expected: ${investment.expectedReturns.toFixed(1)}/tick</span>
                        </div>
                        <Progress value={progress} className="h-1" />
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Investment Requirements */}
        {!canInvest && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-yellow-700">
                <AlertCircle className="w-5 h-5" />
                <div>
                  <div className="font-semibold">Investment Access Locked</div>
                  <div className="text-sm">
                    You need at least 60 reputation to invest in city development. Current:{" "}
                    {player.reputation.toFixed(1)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Investment Opportunities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="w-5 h-5" />
              Investment Opportunities in {city.name}
            </CardTitle>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant={selectedCategory === "all" ? "default" : "outline"}
                onClick={() => setSelectedCategory("all")}
              >
                All ({opportunities.length})
              </Button>
              <Button
                size="sm"
                variant={selectedCategory === "industry" ? "default" : "outline"}
                onClick={() => setSelectedCategory("industry")}
              >
                <Factory className="w-4 h-4 mr-1" />
                Industries ({industryOpportunities.length})
              </Button>
              <Button
                size="sm"
                variant={selectedCategory === "port_upgrade" ? "default" : "outline"}
                onClick={() => setSelectedCategory("port_upgrade")}
              >
                <Anchor className="w-4 h-4 mr-1" />
                Port Upgrades ({portOpportunities.length})
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {filteredOpportunities.map((opportunity) => (
                <div
                  key={opportunity.id}
                  className={`p-4 rounded-lg border transition-all ${
                    opportunity.canBuild && opportunity.canAfford
                      ? "border-green-200 bg-green-50 hover:bg-green-100"
                      : opportunity.canAfford
                        ? "border-yellow-200 bg-yellow-50"
                        : "border-gray-200 bg-gray-50"
                  }`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {getTypeIcon(opportunity.type)}
                      <div>
                        <h4 className="font-semibold">{opportunity.name}</h4>
                        <p className="text-sm text-muted-foreground">{opportunity.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-lg">${opportunity.cost.toLocaleString()}</div>
                      <div className="text-xs text-muted-foreground">{opportunity.buildTime} ticks to build</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-3">
                    <div>
                      <InfoTooltip
                        content="Expected profit per tick once the investment is operational. This is based on current market conditions and may vary."
                        type="info"
                      >
                        <div className="flex items-center gap-1 text-sm cursor-help">
                          <TrendingUp className="w-4 h-4 text-green-600" />
                          <span>Returns: ${opportunity.expectedReturns.toFixed(1)}/tick</span>
                        </div>
                      </InfoTooltip>
                    </div>
                    <div className="flex items-center gap-1 text-sm">
                      {getStatusIcon(opportunity)}
                      <span className={getStatusColor(opportunity)}>{getStatusText(opportunity)}</span>
                    </div>
                  </div>

                  {opportunity.requirements.length > 0 && (
                    <div className="mb-3">
                      <div className="text-sm font-medium text-red-600 mb-1">Requirements:</div>
                      <ul className="text-xs text-red-700 space-y-1">
                        {opportunity.requirements.map((req, idx) => (
                          <li key={idx}>• {req}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="flex justify-between items-center">
                    <div className="text-xs text-muted-foreground">
                      {opportunity.type === "industry" ? "30% profit share" : "Permanent city improvement"}
                    </div>
                    <Button
                      onClick={() => onMakeInvestment(opportunity.type, opportunity.id)}
                      disabled={!canInvest || !opportunity.canBuild || !opportunity.canAfford}
                      className="flex items-center gap-2"
                    >
                      <Hammer className="w-4 h-4" />
                      Invest ${opportunity.cost.toLocaleString()}
                    </Button>
                  </div>
                </div>
              ))}

              {filteredOpportunities.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Building2 className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p className="mb-2">No investment opportunities available</p>
                  <p className="text-sm">
                    {selectedCategory === "all"
                      ? "This city has no available investments at this time."
                      : `No ${selectedCategory.replace("_", " ")} opportunities available.`}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Investment Tips */}
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="space-y-2 text-sm text-blue-700">
              <div className="font-semibold flex items-center gap-2">
                <InfoTooltip
                  content="Tips to help you make better investment decisions and maximize your returns."
                  type="info"
                >
                  <span className="cursor-help">💡 Investment Tips</span>
                </InfoTooltip>
              </div>
              <ul className="space-y-1 text-xs">
                <li>• Higher reputation gives you better investment prices (up to 30% discount)</li>
                <li>• Player-owned industries operate 10% more efficiently than city-owned ones</li>
                <li>• Industries need input materials to operate - check city inventory and trade routes</li>
                <li>• Port upgrades benefit all ships docking at this city, including yours</li>
                <li>• Consider the city's population and wealth when choosing investments</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  )
}
