"use client"

import { useState, useEffect, useRef } from "react"
import { GameEngine } from "../engine/gameEngine"
import type { GameState } from "../types/game"
import { createInitialGameState } from "../data/gameData"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Play,
  Pause,
  Ship,
  Building2,
  DollarSign,
  TrendingUp,
  Anchor,
  BarChart3,
  ShoppingCart,
  Crown,
} from "lucide-react"
import { CONFIG } from "../config/config"
import { TradeRouteVisualizer } from "./trade-route-visualizer"
import { EnhancedCityIndicator } from "./enhanced-city-indicator"
import { EnhancedShipIndicator } from "./enhanced-ship-indicator"
import { TradingInterface } from "./trading-interface"
import { InvestmentInterface } from "./investment-interface"
import { TabbedPanel } from "./tabbed-panel"
import { CityDetailsPanel } from "./city-details-panel"
import { ShipDetailsPanel } from "./ship-details-panel"
import { FleetOverviewPanel } from "./fleet-overview-panel"
import { PlayerStatsPanel } from "./player-stats-panel"
import { getShipType } from "../types/shipTypes"
import { Tooltip, TooltipProvider } from "@/components/ui/tooltip"

const INITIAL_GAME_STATE = createInitialGameState()

export default function Game() {
  const [gameState, setGameState] = useState<GameState>(INITIAL_GAME_STATE)
  const [selectedCity, setSelectedCity] = useState<string | null>(null)
  const [selectedShip, setSelectedShip] = useState<string | null>(null)
  const [priceHistory, setPriceHistory] = useState<{ [cityId: string]: { [goodId: string]: number[] } }>({})
  const gameEngineRef = useRef<GameEngine | null>(null)

  useEffect(() => {
    gameEngineRef.current = new GameEngine(INITIAL_GAME_STATE, (newState) => {
      setGameState(newState)

      // Update price history
      setPriceHistory((prev) => {
        const newHistory = { ...prev }
        newState.cities.forEach((city) => {
          if (!newHistory[city.id]) newHistory[city.id] = {}
          Object.entries(city.inventory).forEach(([goodId, item]) => {
            if (!newHistory[city.id][goodId]) newHistory[city.id][goodId] = []
            newHistory[city.id][goodId].push(item.price)
            if (newHistory[city.id][goodId].length > 20) {
              newHistory[city.id][goodId] = newHistory[city.id][goodId].slice(-20)
            }
          })
        })
        return newHistory
      })
    })

    return () => {
      if (gameEngineRef.current) {
        gameEngineRef.current.stop()
      }
    }
  }, [])

  const toggleGame = () => {
    if (!gameEngineRef.current) return

    if (gameState.isRunning) {
      gameEngineRef.current.stop()
    } else {
      gameEngineRef.current.start()
    }
  }

  const moveShip = (shipId: string, cityId: string) => {
    if (gameEngineRef.current) {
      gameEngineRef.current.moveShip(shipId, cityId)
    }
  }

  const handleTrade = (goodId: string, quantity: number, action: "buy" | "sell") => {
    if (!gameEngineRef.current || !selectedShip) return

    const ship = [...gameState.fleets.flatMap((f) => f.ships), ...gameState.npcFleets.flatMap((f) => f.ships)].find(
      (s) => s.id === selectedShip,
    )

    if (!ship || !ship.currentCity) return

    gameEngineRef.current.tradeGood(selectedShip, ship.currentCity, goodId, quantity, action)
  }

  const handleInvestment = (cityId: string, type: "industry" | "port_upgrade", targetId: string) => {
    if (!gameEngineRef.current) return false
    return gameEngineRef.current.makeInvestment(cityId, type, targetId)
  }

  const getTradeRecommendations = () => {
    if (!gameEngineRef.current || !selectedShip) return []
    return gameEngineRef.current.getTradeRecommendations(selectedShip)
  }

  const selectedCityData = selectedCity ? gameState.cities.find((c) => c.id === selectedCity) : null
  const selectedShipData = selectedShip
    ? [...gameState.fleets.flatMap((f) => f.ships), ...gameState.npcFleets.flatMap((f) => f.ships)].find(
        (s) => s.id === selectedShip,
      )
    : null

  const selectedShipType = selectedShipData ? getShipType(selectedShipData.typeId) : null

  // Get consumption breakdown for selected city
  const consumptionBreakdown =
    selectedCityData && gameEngineRef.current
      ? gameEngineRef.current.getCityConsumptionBreakdown(selectedCityData.id)
      : null

  // Get investment data for selected city
  const investmentOpportunities =
    selectedCityData && gameEngineRef.current ? gameEngineRef.current.getAvailableInvestments(selectedCityData.id) : []

  const investmentSummary = gameEngineRef.current
    ? gameEngineRef.current.getInvestmentSummary()
    : {
        totalInvested: 0,
        totalReturns: 0,
        activeInvestments: 0,
        completedInvestments: 0,
        activeInvestmentsList: [],
        monthlyReturns: 0,
      }

  // Check if selected ship is docked and can trade
  const canTrade =
    selectedShipData && selectedShipData.currentCity && !selectedShipData.destination && !selectedShipData.isNPC

  const dockedCity = canTrade ? gameState.cities.find((c) => c.id === selectedShipData.currentCity) : null

  // All ships for fleet overview
  const allShips = [
    ...gameState.fleets.flatMap((fleet) => fleet.ships.map((ship) => ({ ...ship, isNPC: false }))),
    ...gameState.npcFleets.flatMap((fleet) => fleet.ships.map((ship) => ({ ...ship, isNPC: true }))),
  ]

  // Prepare tabs for the main panel
  const mainPanelTabs = []

  // Trading tab (only show when ship can trade)
  if (canTrade && dockedCity) {
    mainPanelTabs.push({
      id: "trading",
      label: "Trading",
      icon: <ShoppingCart className="w-4 h-4" />,
      content: (
        <TradingInterface
          ship={selectedShipData}
          city={dockedCity}
          goods={gameState.goods}
          player={gameState.player}
          onTrade={handleTrade}
          onGetTradeRecommendations={getTradeRecommendations}
        />
      ),
    })
  }

  // Investment tab (only show when city is selected and player has sufficient reputation)
  if (selectedCityData && consumptionBreakdown) {
    const canInvest = gameState.player.reputation >= 60
    const hasOpportunities = investmentOpportunities.length > 0

    if (canInvest || hasOpportunities) {
      mainPanelTabs.push({
        id: "investment",
        label: "Investment",
        icon: <Crown className="w-4 h-4" />,
        badge: investmentOpportunities.filter((opp) => opp.canAfford && opp.canBuild).length || undefined,
        content: (
          <InvestmentInterface
            city={selectedCityData}
            player={gameState.player}
            opportunities={investmentOpportunities}
            investmentSummary={investmentSummary}
            onMakeInvestment={(type, targetId) => handleInvestment(selectedCityData.id, type, targetId)}
          />
        ),
      })
    }
  }

  // City details tab
  if (selectedCityData && consumptionBreakdown) {
    const industries = gameEngineRef.current ? gameEngineRef.current.getCityIndustries(selectedCityData.id) : []
    mainPanelTabs.push({
      id: "city",
      label: selectedCityData.name,
      icon: <Building2 className="w-4 h-4" />,
      content: (
        <CityDetailsPanel
          city={selectedCityData}
          goods={gameState.goods}
          priceHistory={priceHistory[selectedCityData.id] || {}}
          consumptionBreakdown={consumptionBreakdown}
          industries={industries}
        />
      ),
    })
  }

  // Ship details tab
  if (selectedShipData && selectedShipType) {
    mainPanelTabs.push({
      id: "ship",
      label: selectedShipData.name,
      icon: <Anchor className="w-4 h-4" />,
      badge: selectedShipData.isNPC ? "NPC" : undefined,
      content: (
        <ShipDetailsPanel
          ship={selectedShipData}
          shipType={selectedShipType}
          cities={gameState.cities}
          goods={gameState.goods}
          canTrade={canTrade}
          onMoveShip={(cityId) => moveShip(selectedShipData.id, cityId)}
        />
      ),
    })
  }

  // Fleet overview tab
  mainPanelTabs.push({
    id: "fleet",
    label: "Fleet",
    icon: <Ship className="w-4 h-4" />,
    badge: allShips.filter((s) => !s.isNPC).length,
    content: (
      <FleetOverviewPanel
        ships={allShips}
        cities={gameState.cities}
        onSelectShip={setSelectedShip}
        selectedShip={selectedShip}
      />
    ),
  })

  // Player stats tab
  mainPanelTabs.push({
    id: "stats",
    label: "Statistics",
    icon: <BarChart3 className="w-4 h-4" />,
    content: <PlayerStatsPanel player={gameState.player} goods={gameState.goods} cities={gameState.cities} />,
  })

  // Determine default tab - prioritize investment if available and player can afford something
  let defaultTab = mainPanelTabs[0]?.id
  if (canTrade) {
    defaultTab = "trading"
  } else if (selectedCityData && investmentOpportunities.some((opp) => opp.canAfford && opp.canBuild)) {
    defaultTab = "investment"
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-900 to-blue-700 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 text-center">
          <h1 className="text-4xl font-bold text-white mb-2">Port Royale Economic Simulator</h1>
          <div className="flex items-center justify-center gap-4 text-white">
            <span>Tick: {gameState.currentTick}</span>
            <span className="text-sm opacity-75">({CONFIG.GAME.TICK_INTERVAL_MS / 1000}s intervals)</span>
            <TooltipProvider>
              <Tooltip
                content={`Your current money: $${gameState.player.money.toLocaleString()}. Use this to buy goods and ships. Money is earned by selling goods at higher prices than you bought them.`}
              >
                <div className="flex items-center gap-2 bg-green-600 px-3 py-1 rounded cursor-help">
                  <DollarSign className="w-4 h-4" />
                  <span className="font-semibold">${gameState.player.money.toLocaleString()}</span>
                </div>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider>
              <Tooltip
                content={`Your trading reputation: ${gameState.player.reputation.toFixed(0)}/100. Higher reputation gives better buying prices and investment discounts. Reputation increases with successful trades.`}
              >
                <div className="flex items-center gap-2 bg-blue-600 px-3 py-1 rounded cursor-help">
                  <TrendingUp className="w-4 h-4" />
                  <span className="text-sm">Rep: {gameState.player.reputation.toFixed(0)}</span>
                </div>
              </Tooltip>
            </TooltipProvider>
            {/* Investment Summary in Header */}
            {investmentSummary.totalInvested > 0 && (
              <TooltipProvider>
                <Tooltip
                  content={`Total invested: $${investmentSummary.totalInvested.toLocaleString()}. Monthly returns: $${investmentSummary.monthlyReturns.toLocaleString()}. Active investments: ${investmentSummary.activeInvestments}`}
                >
                  <div className="flex items-center gap-2 bg-purple-600 px-3 py-1 rounded cursor-help">
                    <Crown className="w-4 h-4" />
                    <span className="text-sm">${investmentSummary.totalInvested.toLocaleString()} invested</span>
                  </div>
                </Tooltip>
              </TooltipProvider>
            )}
            <Button
              onClick={toggleGame}
              variant={gameState.isRunning ? "destructive" : "default"}
              className="flex items-center gap-2"
            >
              {gameState.isRunning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              {gameState.isRunning ? "Pause" : "Start"}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Map Section */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="w-5 h-5" />
                  Caribbean Trade Map
                  <TooltipProvider>
                    <Tooltip
                      content={`NPC Trading Activity: ${gameState.npcFleets.length} active fleets with ${gameState.npcFleets.reduce((sum, fleet) => sum + fleet.ships.length, 0)} ships. NPCs automatically trade to balance the economy.`}
                    >
                      <Badge variant="outline" className="ml-auto cursor-help">
                        {gameState.npcFleets.length} NPC Fleets •{" "}
                        {gameState.npcFleets.reduce((sum, fleet) => sum + fleet.ships.length, 0)} Ships
                      </Badge>
                    </Tooltip>
                  </TooltipProvider>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  className="relative bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg overflow-hidden border-2 border-blue-300"
                  style={{ height: `${CONFIG.MAP.HEIGHT}px` }}
                >
                  {/* Trade route visualization */}
                  <TradeRouteVisualizer
                    cities={gameState.cities}
                    ships={gameState.fleets.flatMap((f) => f.ships)}
                    npcFleets={gameState.npcFleets}
                    mapWidth={CONFIG.MAP.WIDTH}
                    mapHeight={CONFIG.MAP.HEIGHT}
                  />

                  {/* Enhanced Cities */}
                  {gameState.cities.map((city) => (
                    <EnhancedCityIndicator
                      key={city.id}
                      city={city}
                      goods={gameState.goods}
                      isSelected={selectedCity === city.id}
                      onClick={() => setSelectedCity(city.id)}
                    />
                  ))}

                  {/* Enhanced Ships */}
                  {allShips.map((ship) => {
                    const currentCity = gameState.cities.find((c) => c.id === ship.currentCity)
                    const destinationCity = ship.destination
                      ? gameState.cities.find((c) => c.id === ship.destination)
                      : null

                    let position = currentCity?.position || { x: 0, y: 0 }

                    if (destinationCity && ship.progress > 0) {
                      position = {
                        x:
                          currentCity!.position.x +
                          (destinationCity.position.x - currentCity!.position.x) * ship.progress,
                        y:
                          currentCity!.position.y +
                          (destinationCity.position.y - currentCity!.position.y) * ship.progress,
                      }
                    }

                    return (
                      <EnhancedShipIndicator
                        key={ship.id}
                        ship={ship}
                        position={position}
                        isSelected={selectedShip === ship.id}
                        onClick={() => setSelectedShip(ship.id)}
                      />
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* NPC Fleet Status - Moved below map */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Ship className="w-5 h-5 text-orange-600" />
                  NPC Trading Activity
                  <Badge variant="secondary" className="ml-auto">
                    {gameState.npcFleets.length}/{CONFIG.NPC_FLEETS.MAX_ACTIVE_NPC_FLEETS}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm max-h-48 overflow-y-auto">
                  {gameState.npcFleets.map((fleet) => (
                    <div
                      key={fleet.id}
                      className="p-3 bg-gradient-to-r from-orange-50 to-orange-100 rounded border border-orange-200"
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-orange-800">{fleet.name}</span>
                        <Badge variant="outline" className="text-xs bg-white">
                          {fleet.mission.status.replace("_", " ")}
                        </Badge>
                      </div>
                      <div className="text-xs text-orange-600 space-y-1">
                        <p className="flex items-center gap-1">
                          <span className="font-semibold">{fleet.ships.length} ships</span>
                          <span>•</span>
                          <span>{fleet.mission.goodId}</span>
                          <span>•</span>
                          <span>{gameState.cities.find((c) => c.id === fleet.mission.originCityId)?.name}</span>
                          <span>→</span>
                          <span>{gameState.cities.find((c) => c.id === fleet.mission.destinationCityId)?.name}</span>
                        </p>
                        <div className="flex justify-between">
                          <span>Capacity: {fleet.totalCapacity}</span>
                          <span>Speed: {fleet.effectiveSpeed}</span>
                          <span>Profit: ${fleet.mission.expectedProfit.toFixed(0)}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                  {gameState.npcFleets.length === 0 && (
                    <p className="text-muted-foreground text-center py-4 bg-gray-50 rounded">No active NPC fleets</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Information Panel */}
          <div>
            {mainPanelTabs.length > 0 ? (
              <TabbedPanel title="Game Information" tabs={mainPanelTabs} defaultTab={defaultTab} />
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Welcome to Port Royale</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    <Building2 className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p className="mb-2">Click on a city or ship to view details</p>
                    <p className="text-sm">Start the simulation to begin trading!</p>
                    <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-semibold text-blue-800 mb-2">💡 Getting Started</h4>
                      <ul className="text-sm text-blue-700 space-y-1 text-left">
                        <li>• Click cities to see market prices and investment opportunities</li>
                        <li>• Click ships to manage cargo and trading</li>
                        <li>• Build reputation through successful trades</li>
                        <li>• Invest in city development once you reach 60 reputation</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
