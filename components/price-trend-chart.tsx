interface PriceTrendChartProps {
  prices: number[]
  basePrice: number
  width?: number
  height?: number
}

export function PriceTrendChart({ prices, basePrice, width = 60, height = 20 }: PriceTrendChartProps) {
  if (prices.length < 2) {
    return <div className="w-15 h-5 bg-gray-100 rounded" />
  }

  const minPrice = Math.min(...prices)
  const maxPrice = Math.max(...prices)
  const priceRange = maxPrice - minPrice || 1

  // Generate SVG path
  const pathData = prices
    .map((price, index) => {
      const x = (index / (prices.length - 1)) * width
      const y = height - ((price - minPrice) / priceRange) * height
      return `${index === 0 ? "M" : "L"} ${x} ${y}`
    })
    .join(" ")

  const currentPrice = prices[prices.length - 1]
  const isUp = currentPrice > basePrice
  const strokeColor = isUp ? "#ef4444" : "#10b981"
  const fillColor = isUp ? "#fef2f2" : "#f0fdf4"

  return (
    <div className="relative">
      <svg width={width} height={height} className="overflow-visible">
        <defs>
          <linearGradient id={`gradient-${Math.random()}`} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={strokeColor} stopOpacity="0.3" />
            <stop offset="100%" stopColor={strokeColor} stopOpacity="0.1" />
          </linearGradient>
        </defs>

        {/* Fill area */}
        <path d={`${pathData} L ${width} ${height} L 0 ${height} Z`} fill={`url(#gradient-${Math.random()})`} />

        {/* Price line */}
        <path d={pathData} stroke={strokeColor} strokeWidth="2" fill="none" />

        {/* Current price dot */}
        <circle cx={width} cy={height - ((currentPrice - minPrice) / priceRange) * height} r="2" fill={strokeColor} />
      </svg>
    </div>
  )
}
