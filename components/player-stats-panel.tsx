"use client"
import { DollarSign, TrendingUp, BarChart3 } from "lucide-react"
import type { PlayerStats, Good, City } from "../types/game"

interface PlayerStatsPanelProps {
  player: PlayerStats
  goods: Good[]
  cities: City[]
}

export function PlayerStatsPanel({ player, goods, cities }: PlayerStatsPanelProps) {
  const overviewContent = (
    <div className="grid grid-cols-2 gap-4 text-sm">
      <div>
        <span className="text-muted-foreground">Current Money:</span>
        <div className="font-semibold text-green-600 text-lg">${player.money.toLocaleString()}</div>
      </div>
      <div>
        <span className="text-muted-foreground">Total Profit:</span>
        <div className="font-semibold text-green-600">${player.totalProfit.toLocaleString()}</div>
      </div>
      <div>
        <span className="text-muted-foreground">Total Trades:</span>
        <div className="font-semibold">{player.totalTrades}</div>
      </div>
      <div>
        <span className="text-muted-foreground">Reputation:</span>
        <div className="font-semibold text-blue-600">{player.reputation.toFixed(1)}/100</div>
      </div>
    </div>
  )

  const transactionsContent = (
    <div className="space-y-1 max-h-64 overflow-y-auto">
      {player.tradeHistory
        .slice(-10)
        .reverse()
        .map((transaction) => {
          const good = goods.find((g) => g.id === transaction.goodId)
          const city = cities.find((c) => c.id === transaction.cityId)
          return (
            <div key={transaction.id} className="text-xs p-2 bg-gray-50 rounded">
              <div className="flex justify-between">
                <span>
                  {transaction.action === "buy" ? "Bought" : "Sold"} {transaction.quantity} {good?.name}
                </span>
                <span className={transaction.action === "buy" ? "text-red-600" : "text-green-600"}>
                  {transaction.action === "buy" ? "-" : "+"}${transaction.totalValue}
                </span>
              </div>
              <div className="text-muted-foreground">
                {city?.name} • ${transaction.pricePerUnit}/unit
                {transaction.profit && ` • Profit: $${transaction.profit.toFixed(0)}`}
              </div>
            </div>
          )
        })}
      {player.tradeHistory.length === 0 && (
        <p className="text-muted-foreground text-center py-4 bg-gray-50 rounded">No transactions yet</p>
      )}
    </div>
  )

  const analyticsContent = (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-muted-foreground">Recent Trades:</span>
          <div className="font-semibold">{player.tradeHistory.slice(-10).length}</div>
        </div>
        <div>
          <span className="text-muted-foreground">Recent Profit:</span>
          <div className="font-semibold text-green-600">
            $
            {player.tradeHistory
              .slice(-10)
              .filter((t) => t.action === "sell")
              .reduce((sum, t) => sum + (t.profit || 0), 0)
              .toLocaleString()}
          </div>
        </div>
        <div>
          <span className="text-muted-foreground">Avg Trade Value:</span>
          <div className="font-semibold">
            $
            {player.totalTrades > 0
              ? Math.round(
                  player.tradeHistory.reduce((sum, t) => sum + t.totalValue, 0) / player.totalTrades,
                ).toLocaleString()
              : 0}
          </div>
        </div>
        <div>
          <span className="text-muted-foreground">Success Rate:</span>
          <div className="font-semibold text-blue-600">
            {player.totalTrades > 0
              ? (
                  (player.tradeHistory.filter((t) => t.action === "sell" && (t.profit || 0) > 0).length /
                    player.tradeHistory.filter((t) => t.action === "sell").length) *
                  100
                ).toFixed(0)
              : 0}
            %
          </div>
        </div>
      </div>

      {/* Reputation Progress Bar */}
      <div>
        <div className="flex justify-between text-sm mb-1">
          <span>Reputation Progress</span>
          <span>{player.reputation.toFixed(1)}/100</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${player.reputation}%` }}
          />
        </div>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      <div>
        <h4 className="font-semibold mb-3 flex items-center gap-2">
          <DollarSign className="w-4 h-4" />
          Financial Overview
        </h4>
        {overviewContent}
      </div>

      <div>
        <h4 className="font-semibold mb-3 flex items-center gap-2">
          <TrendingUp className="w-4 h-4" />
          Recent Transactions
        </h4>
        {transactionsContent}
      </div>

      <div>
        <h4 className="font-semibold mb-3 flex items-center gap-2">
          <BarChart3 className="w-4 h-4" />
          Performance Analytics
        </h4>
        {analyticsContent}
      </div>
    </div>
  )
}
