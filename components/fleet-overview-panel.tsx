"use client"

import type React from "react"

import { Badge } from "@/components/ui/badge"
import { Ship, Anchor } from "lucide-react"
import type { Ship as ShipType, City } from "../types/game"
import { getShipType } from "../types/shipTypes"

interface FleetOverviewPanelProps {
  ships: ShipType[]
  cities: City[]
  onSelectShip: (shipId: string) => void
  selectedShip: string | null
}

export function FleetOverviewPanel({ ships, cities, onSelectShip, selectedShip }: FleetOverviewPanelProps) {
  const playerShips = ships.filter((ship) => !ship.isNPC)
  const npcShips = ships.filter((ship) => ship.isNPC)

  const renderShipList = (shipList: ShipType[], title: string, icon: React.ReactNode) => (
    <div className="space-y-2">
      <h4 className="font-semibold flex items-center gap-2">
        {icon}
        {title} ({shipList.length})
      </h4>
      <div className="space-y-2 max-h-48 overflow-y-auto">
        {shipList.map((ship) => {
          const cargoCount = Object.values(ship.cargo).reduce((sum, qty) => sum + qty, 0)
          const shipType = getShipType(ship.typeId)
          return (
            <button
              key={ship.id}
              onClick={() => onSelectShip(ship.id)}
              className={`w-full text-left p-3 rounded border transition-all ${
                selectedShip === ship.id ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <span className="font-medium">{ship.name}</span>
                  <div className="text-xs text-muted-foreground">{shipType?.name}</div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={ship.destination ? "default" : "secondary"}>
                    {ship.destination ? "Traveling" : "Docked"}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {cargoCount}/{ship.capacity}
                  </Badge>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                {ship.destination
                  ? `→ ${cities.find((c) => c.id === ship.destination)?.name}`
                  : `@ ${cities.find((c) => c.id === ship.currentCity)?.name}`}
              </p>
              {ship.destination && (
                <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                  <div
                    className="bg-blue-500 h-1 rounded-full transition-all duration-1000"
                    style={{ width: `${ship.progress * 100}%` }}
                  />
                </div>
              )}
            </button>
          )
        })}
        {shipList.length === 0 && (
          <p className="text-muted-foreground text-center py-4 bg-gray-50 rounded">No ships in this category</p>
        )}
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {renderShipList(playerShips, "Your Fleet", <Anchor className="w-4 h-4 text-green-600" />)}
      {renderShipList(npcShips, "NPC Ships", <Ship className="w-4 h-4 text-orange-600" />)}
    </div>
  )
}
