"use client"
import type { City, Good } from "../types/game"
import { TrendingUp, TrendingDown, AlertTriangle, CheckCircle } from "lucide-react"

interface EnhancedCityIndicatorProps {
  city: City
  goods: Good[]
  isSelected: boolean
  onClick: () => void
}

export function EnhancedCityIndicator({ city, goods, isSelected, onClick }: EnhancedCityIndicatorProps) {
  // Calculate economic health
  const economicHealth = calculateEconomicHealth(city, goods)
  const { status, color, pulseColor } = getStatusColor(economicHealth)

  // Check for critical shortages or surpluses
  const criticalItems = getCriticalItems(city, goods)

  return (
    <button
      onClick={onClick}
      className={`absolute transform -translate-x-1/2 -translate-y-1/2 ${isSelected ? "ring-4 ring-yellow-400" : ""}`}
      style={{
        left: `${city.position.x}px`,
        top: `${city.position.y}px`,
      }}
    >
      {/* Main city circle */}
      <div
        className={`rounded-full w-10 h-10 flex items-center justify-center text-white font-bold shadow-lg transition-all duration-300 ${color}`}
        style={{
          animation: economicHealth < 0.3 ? `pulse 2s infinite` : undefined,
        }}
      >
        {city.name.charAt(0)}
      </div>

      {/* Economic status indicator */}
      <div className="absolute -top-1 -right-1">
        {economicHealth > 0.8 ? (
          <CheckCircle className="w-4 h-4 text-green-500 bg-white rounded-full" />
        ) : economicHealth < 0.3 ? (
          <AlertTriangle className="w-4 h-4 text-red-500 bg-white rounded-full" />
        ) : null}
      </div>

      {/* Critical item indicators */}
      {criticalItems.surplus.length > 0 && (
        <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 rounded-full border border-white">
          <TrendingDown className="w-2 h-2 text-white m-0.5" />
        </div>
      )}

      {criticalItems.shortage.length > 0 && (
        <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-red-500 rounded-full border border-white">
          <TrendingUp className="w-2 h-2 text-white m-0.5" />
        </div>
      )}

      {/* City name with status */}
      <div className="absolute top-12 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs whitespace-nowrap">
        <div>{city.name}</div>
        <div className="text-xs opacity-75">{status}</div>
      </div>

      {/* Population indicator */}
      <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs font-semibold text-gray-700 bg-white bg-opacity-90 px-1 rounded">
        {(city.population / 1000).toFixed(0)}k
      </div>
    </button>
  )
}

function calculateEconomicHealth(city: City, goods: Good[]): number {
  let totalHealth = 0
  let itemCount = 0

  Object.entries(city.inventory).forEach(([goodId, item]) => {
    const good = goods.find((g) => g.id === goodId)
    if (!good) return

    // Health based on price stability and inventory levels
    const priceRatio = item.price / good.basePrice
    const priceHealth = Math.max(0, 1 - Math.abs(priceRatio - 1))

    const inventoryHealth = Math.min(1, item.quantity / 100)

    totalHealth += (priceHealth + inventoryHealth) / 2
    itemCount++
  })

  return itemCount > 0 ? totalHealth / itemCount : 0.5
}

function getStatusColor(health: number) {
  if (health > 0.8) {
    return {
      status: "Prosperous",
      color: "bg-green-600 hover:bg-green-700",
      pulseColor: "bg-green-400",
    }
  } else if (health > 0.6) {
    return {
      status: "Stable",
      color: "bg-blue-600 hover:bg-blue-700",
      pulseColor: "bg-blue-400",
    }
  } else if (health > 0.4) {
    return {
      status: "Struggling",
      color: "bg-yellow-600 hover:bg-yellow-700",
      pulseColor: "bg-yellow-400",
    }
  } else if (health > 0.2) {
    return {
      status: "Poor",
      color: "bg-orange-600 hover:bg-orange-700",
      pulseColor: "bg-orange-400",
    }
  } else {
    return {
      status: "Crisis",
      color: "bg-red-600 hover:bg-red-700",
      pulseColor: "bg-red-400",
    }
  }
}

function getCriticalItems(city: City, goods: Good[]) {
  const surplus: string[] = []
  const shortage: string[] = []

  Object.entries(city.inventory).forEach(([goodId, item]) => {
    if (item.quantity > 150) surplus.push(goodId)
    if (item.quantity < 20) shortage.push(goodId)
  })

  return { surplus, shortage }
}
