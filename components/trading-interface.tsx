"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { ShoppingCart, DollarSign, TrendingUp, AlertCircle, CheckCircle, Calculator } from "lucide-react"
import type { City, Ship, Good, PlayerStats } from "../types/game"
import { TooltipProvider } from "@/components/ui/tooltip"
import { InfoTooltip } from "./info-tooltip"

interface TradingInterfaceProps {
  ship: Ship
  city: City
  goods: Good[]
  player: PlayerStats
  onTrade: (goodId: string, quantity: number, action: "buy" | "sell") => void
  onGetTradeRecommendations: () => TradeRecommendation[]
}

interface TradeRecommendation {
  goodId: string
  action: "buy" | "sell"
  quantity: number
  currentPrice: number
  targetCityId: string
  targetPrice: number
  profitPotential: number
  confidence: "high" | "medium" | "low"
}

export function TradingInterface({
  ship,
  city,
  goods,
  player,
  onTrade,
  onGetTradeRecommendations,
}: TradingInterfaceProps) {
  const [selectedGood, setSelectedGood] = useState<string | null>(null)
  const [tradeQuantity, setTradeQuantity] = useState<number>(1)
  const [showRecommendations, setShowRecommendations] = useState(false)

  const recommendations = onGetTradeRecommendations()
  const cargoUsed = Object.values(ship.cargo).reduce((sum, qty) => sum + qty, 0)
  const cargoSpace = ship.capacity - cargoUsed

  const calculateTradeCost = (goodId: string, quantity: number, action: "buy" | "sell"): number => {
    const cityItem = city.inventory[goodId]
    if (!cityItem) return 0

    // Apply reputation modifier to prices
    const reputationModifier = 1 + (player.reputation - 50) * 0.002 // ±10% based on reputation
    const effectivePrice = Math.round(cityItem.price * (action === "buy" ? reputationModifier : 2 - reputationModifier))

    return effectivePrice * quantity
  }

  const canAffordTrade = (goodId: string, quantity: number): boolean => {
    const cost = calculateTradeCost(goodId, quantity, "buy")
    return player.money >= cost
  }

  const getMaxAffordableQuantity = (goodId: string): number => {
    const cityItem = city.inventory[goodId]
    if (!cityItem) return 0

    const pricePerUnit = calculateTradeCost(goodId, 1, "buy")
    const maxByMoney = Math.floor(player.money / pricePerUnit)
    const maxByInventory = cityItem.quantity
    const maxBySpace = cargoSpace

    return Math.min(maxByMoney, maxByInventory, maxBySpace)
  }

  const getMaxSellableQuantity = (goodId: string): number => {
    return ship.cargo[goodId] || 0
  }

  const handleTrade = (action: "buy" | "sell") => {
    if (!selectedGood || tradeQuantity <= 0) return

    const cityItem = city.inventory[selectedGood]
    if (!cityItem) return

    if (action === "buy") {
      const maxQuantity = getMaxAffordableQuantity(selectedGood)
      const actualQuantity = Math.min(tradeQuantity, maxQuantity)
      if (actualQuantity > 0) {
        onTrade(selectedGood, actualQuantity, "buy")
        setTradeQuantity(1)
      }
    } else {
      const maxQuantity = getMaxSellableQuantity(selectedGood)
      const actualQuantity = Math.min(tradeQuantity, maxQuantity)
      if (actualQuantity > 0) {
        onTrade(selectedGood, actualQuantity, "sell")
        setTradeQuantity(1)
      }
    }
  }

  const getProfitIndicator = (goodId: string): { profit: number; confidence: string } => {
    const recommendation = recommendations.find((r) => r.goodId === goodId)
    if (recommendation) {
      return {
        profit: recommendation.profitPotential,
        confidence: recommendation.confidence,
      }
    }
    return { profit: 0, confidence: "unknown" }
  }

  return (
    <TooltipProvider>
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="w-5 h-5" />
            Trading at {city.name}
            <Badge variant="outline" className="ml-auto">
              {ship.name}
            </Badge>
          </CardTitle>
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>
              Cargo: {cargoUsed}/{ship.capacity}
            </span>
            <InfoTooltip
              content={`Your reputation affects trade prices. Current: ${player.reputation.toFixed(1)}/100. Higher reputation = better prices when buying, worse when selling.`}
              type="info"
            >
              <span className="flex items-center gap-1 cursor-help">
                <DollarSign className="w-4 h-4" />${player.money.toLocaleString()}
              </span>
            </InfoTooltip>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Market Overview */}
          <div className="grid grid-cols-1 gap-2 max-h-64 overflow-y-auto">
            {goods.map((good) => {
              const cityItem = city.inventory[good.id]
              const shipCargo = ship.cargo[good.id] || 0
              const profitInfo = getProfitIndicator(good.id)

              if (!cityItem) return null

              const isSelected = selectedGood === good.id
              const canBuy = cityItem.quantity > 0 && cargoSpace > 0 && canAffordTrade(good.id, 1)
              const canSell = shipCargo > 0

              return (
                <button
                  key={good.id}
                  onClick={() => setSelectedGood(good.id)}
                  className={`p-3 rounded border text-left transition-all ${
                    isSelected ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{good.name}</span>
                      {profitInfo.profit > 0 && (
                        <InfoTooltip
                          content={`Potential profit: $${profitInfo.profit.toFixed(0)} with ${profitInfo.confidence} confidence based on price differences to other cities.`}
                          type="success"
                        >
                          <Badge
                            variant={profitInfo.confidence === "high" ? "default" : "secondary"}
                            className="text-xs cursor-help"
                          >
                            +${profitInfo.profit.toFixed(0)}
                          </Badge>
                        </InfoTooltip>
                      )}
                    </div>
                    <span className="font-semibold">${cityItem.price}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center gap-4">
                      <span>City: {Math.round(cityItem.quantity)}</span>
                      <span>Ship: {shipCargo}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {canBuy && <CheckCircle className="w-4 h-4 text-green-500" />}
                      {canSell && <TrendingUp className="w-4 h-4 text-blue-500" />}
                      {!canBuy && !canSell && <AlertCircle className="w-4 h-4 text-gray-400" />}
                    </div>
                  </div>
                </button>
              )
            })}
          </div>

          {/* Trading Controls */}
          {selectedGood && (
            <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold">Trading {goods.find((g) => g.id === selectedGood)?.name}</h4>
                <Badge variant="outline">${city.inventory[selectedGood]?.price || 0} per unit</Badge>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Quantity</label>
                  <Input
                    type="number"
                    min="1"
                    value={tradeQuantity}
                    onChange={(e) => setTradeQuantity(Math.max(1, Number.parseInt(e.target.value) || 1))}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Total Cost</label>
                  <div className="p-2 bg-white rounded border">
                    ${calculateTradeCost(selectedGood, tradeQuantity, "buy").toLocaleString()}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <Button
                  onClick={() => handleTrade("buy")}
                  disabled={
                    !canAffordTrade(selectedGood, tradeQuantity) ||
                    tradeQuantity > getMaxAffordableQuantity(selectedGood)
                  }
                  className="flex items-center gap-2"
                >
                  <ShoppingCart className="w-4 h-4" />
                  Buy (Max: {getMaxAffordableQuantity(selectedGood)})
                </Button>

                <Button
                  onClick={() => handleTrade("sell")}
                  disabled={tradeQuantity > getMaxSellableQuantity(selectedGood)}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <DollarSign className="w-4 h-4" />
                  Sell (Max: {getMaxSellableQuantity(selectedGood)})
                </Button>
              </div>

              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setTradeQuantity(Math.min(10, getMaxAffordableQuantity(selectedGood)))}
                >
                  10
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setTradeQuantity(Math.min(50, getMaxAffordableQuantity(selectedGood)))}
                >
                  50
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setTradeQuantity(getMaxAffordableQuantity(selectedGood))}
                >
                  Max Buy
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setTradeQuantity(getMaxSellableQuantity(selectedGood))}
                >
                  Max Sell
                </Button>
              </div>
            </div>
          )}

          <Separator />

          {/* Trade Recommendations */}
          <div>
            <InfoTooltip
              content="Trade recommendations analyze price differences across all cities to suggest profitable opportunities. Confidence levels indicate reliability of the profit estimates."
              type="help"
            >
              <Button
                variant="outline"
                onClick={() => setShowRecommendations(!showRecommendations)}
                className="w-full flex items-center gap-2"
              >
                <Calculator className="w-4 h-4" />
                {showRecommendations ? "Hide" : "Show"} Trade Recommendations
              </Button>
            </InfoTooltip>

            {showRecommendations && (
              <div className="mt-4 space-y-2">
                {recommendations.length > 0 ? (
                  recommendations.slice(0, 5).map((rec, index) => {
                    const good = goods.find((g) => g.id === rec.goodId)
                    if (!good) return null

                    return (
                      <div key={index} className="p-3 bg-white rounded border">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium">{good.name}</span>
                          <Badge variant={rec.confidence === "high" ? "default" : "secondary"} className="text-xs">
                            {rec.confidence} confidence
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <p>
                            {rec.action === "buy" ? "Buy" : "Sell"} {rec.quantity} units at ${rec.currentPrice}
                          </p>
                          <p>
                            Target: {rec.targetCityId} (${rec.targetPrice}) - Profit: ${rec.profitPotential.toFixed(0)}
                          </p>
                        </div>
                      </div>
                    )
                  })
                ) : (
                  <p className="text-center text-muted-foreground py-4">
                    No profitable opportunities found at this time
                  </p>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  )
}
