"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle, AlertCircle } from "lucide-react"
import type { Ship, City, Good } from "../types/game"
import type { ShipType } from "../types/shipTypes"
import { TooltipProvider } from "@/components/ui/tooltip"
import { InfoTooltip } from "./info-tooltip"

interface ShipDetailsPanelProps {
  ship: Ship
  shipType: ShipType
  cities: City[]
  goods: Good[]
  canTrade: boolean
  onMoveShip: (cityId: string) => void
}

export function ShipDetailsPanel({ ship, shipType, cities, goods, canTrade, onMoveShip }: ShipDetailsPanelProps) {
  const statusContent = (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <InfoTooltip
          content={`Cargo space: ${Object.values(ship.cargo).reduce((a, b) => a + b, 0)} of ${ship.capacity} units used. Each good takes 1 unit of space.`}
          type="info"
        >
          <div className="space-y-2 cursor-help">
            <div className="flex justify-between text-sm">
              <span>Cargo:</span>
              <span className="font-semibold">
                {Object.values(ship.cargo).reduce((a, b) => a + b, 0)}/{ship.capacity}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full"
                style={{
                  width: `${(Object.values(ship.cargo).reduce((a, b) => a + b, 0) / ship.capacity) * 100}%`,
                }}
              />
            </div>
          </div>
        </InfoTooltip>
        <InfoTooltip
          content={`Ship speed: ${ship.speed} units. Higher speed means faster travel between cities. Speed can vary from the base ship type due to modifications and conditions.`}
          type="info"
        >
          <div className="space-y-2 cursor-help">
            <div className="flex justify-between text-sm">
              <span>Speed:</span>
              <span className="font-semibold">{ship.speed}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{ width: `${(ship.speed / 100) * 100}%` }} />
            </div>
          </div>
        </InfoTooltip>
      </div>

      <div className="text-sm">
        {ship.destination ? (
          <div className="space-y-2">
            <p className="flex items-center gap-2">
              <span>Traveling to:</span>
              <Badge variant="outline">{cities.find((c) => c.id === ship.destination)?.name}</Badge>
            </p>
            <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
              <div
                className="bg-gradient-to-r from-blue-500 to-blue-600 h-full rounded-full transition-all duration-1000 flex items-center justify-center text-xs text-white font-semibold"
                style={{ width: `${ship.progress * 100}%` }}
              >
                {(ship.progress * 100).toFixed(0)}%
              </div>
            </div>
          </div>
        ) : (
          <p className="flex items-center gap-2">
            <span>Docked at:</span>
            <Badge variant="default">{cities.find((c) => c.id === ship.currentCity)?.name}</Badge>
            {canTrade && <CheckCircle className="w-4 h-4 text-green-500" />}
          </p>
        )}
      </div>
    </div>
  )

  const cargoContent = (
    <div className="space-y-1 text-sm">
      {Object.entries(ship.cargo).map(([goodId, quantity]) => {
        const good = goods.find((g) => g.id === goodId)
        return good && quantity > 0 ? (
          <div key={goodId} className="flex justify-between items-center bg-gray-50 p-2 rounded">
            <span className="font-medium">{good.name}</span>
            <div className="flex items-center gap-2">
              <div className="w-16 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full"
                  style={{ width: `${(quantity / ship.capacity) * 100}%` }}
                />
              </div>
              <Badge variant="secondary">{quantity}</Badge>
            </div>
          </div>
        ) : null
      })}
      {Object.values(ship.cargo).every((q) => q === 0) && (
        <p className="text-muted-foreground text-center py-4 bg-gray-50 rounded">Empty cargo hold</p>
      )}
    </div>
  )

  const shipInfoContent = (
    <div className="space-y-4">
      <div className="space-y-1 text-sm">
        <p className="flex items-center gap-2">
          <span className="font-medium">{shipType.name}</span>
          <Badge variant="outline" className="text-xs">
            {shipType.era}
          </Badge>
        </p>
        <p className="text-muted-foreground">{shipType.description}</p>
      </div>

      <div className="grid grid-cols-2 gap-4 text-xs">
        <div>
          <h5 className="font-semibold text-green-600 mb-1">Advantages</h5>
          <ul className="space-y-1">
            {shipType.advantages.map((advantage, index) => (
              <li key={index} className="text-green-700">
                • {advantage}
              </li>
            ))}
          </ul>
        </div>
        <div>
          <h5 className="font-semibold text-red-600 mb-1">Disadvantages</h5>
          <ul className="space-y-1">
            {shipType.disadvantages.map((disadvantage, index) => (
              <li key={index} className="text-red-700">
                • {disadvantage}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )

  const actionsContent = (
    <div className="space-y-4">
      {!ship.destination && ship.currentCity && !ship.isNPC && (
        <div>
          <h4 className="font-semibold mb-2">Quick Travel</h4>
          <div className="grid grid-cols-2 gap-2">
            {cities
              .filter((c) => c.id !== ship.currentCity)
              .slice(0, 6)
              .map((city) => (
                <Button
                  key={city.id}
                  size="sm"
                  variant="outline"
                  onClick={() => onMoveShip(city.id)}
                  className="text-xs"
                >
                  → {city.name}
                </Button>
              ))}
          </div>
        </div>
      )}

      {canTrade && (
        <div className="p-3 bg-green-50 rounded border border-green-200">
          <div className="flex items-center gap-2 text-green-700">
            <CheckCircle className="w-4 h-4" />
            <span className="font-semibold">Trading Available</span>
          </div>
          <p className="text-sm text-green-600 mt-1">Ship is docked and ready to trade goods</p>
        </div>
      )}

      {ship.isNPC && (
        <div className="p-3 bg-orange-50 rounded border border-orange-200">
          <div className="flex items-center gap-2 text-orange-700">
            <AlertCircle className="w-4 h-4" />
            <span className="font-semibold">NPC Vessel</span>
          </div>
          <p className="text-sm text-orange-600 mt-1">This ship is controlled by AI traders</p>
        </div>
      )}
    </div>
  )

  return (
    <TooltipProvider>
      <div className="space-y-2">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold">{ship.name}</h3>
          <div className="flex items-center gap-2">
            {ship.isNPC && <Badge variant="secondary">NPC</Badge>}
            {canTrade && (
              <Badge variant="default" className="bg-green-600">
                Can Trade
              </Badge>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Status</h4>
            {statusContent}
          </div>
          <div>
            <h4 className="font-semibold mb-2">Cargo Hold</h4>
            {cargoContent}
          </div>
          <div>
            <h4 className="font-semibold mb-2">Ship Information</h4>
            {shipInfoContent}
          </div>
          <div>
            <h4 className="font-semibold mb-2">Actions</h4>
            {actionsContent}
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
