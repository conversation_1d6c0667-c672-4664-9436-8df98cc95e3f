"use client"
import { Ship, Package, Clock } from "lucide-react"
import type { Ship as ShipType } from "../types/game"

interface EnhancedShipIndicatorProps {
  ship: ShipType & { isNPC?: boolean }
  position: { x: number; y: number }
  isSelected: boolean
  onClick: () => void
}

export function EnhancedShipIndicator({ ship, position, isSelected, onClick }: EnhancedShipIndicatorProps) {
  const cargoCount = Object.values(ship.cargo).reduce((sum, qty) => sum + qty, 0)
  const cargoPercentage = (cargoCount / ship.capacity) * 100

  return (
    <button
      onClick={onClick}
      className={`absolute transform -translate-x-1/2 -translate-y-1/2 ${isSelected ? "ring-2 ring-green-400" : ""}`}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }}
    >
      {/* Ship icon with cargo indicator */}
      <div className="relative">
        <Ship
          className={`w-8 h-8 ${
            ship.isNPC ? "text-orange-600 hover:text-orange-700" : "text-green-600 hover:text-green-700"
          } transition-colors duration-200`}
        />

        {/* NPC indicator */}
        {ship.isNPC && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-500 rounded-full border border-white" />
        )}

        {/* Cargo level indicator */}
        {cargoCount > 0 && (
          <div className="absolute -bottom-1 -right-1 flex items-center">
            <div
              className={`w-3 h-3 rounded-full border border-white ${
                cargoPercentage > 80 ? "bg-red-500" : cargoPercentage > 50 ? "bg-yellow-500" : "bg-green-500"
              }`}
            >
              <Package className="w-2 h-2 text-white m-0.5" />
            </div>
          </div>
        )}

        {/* Travel progress indicator */}
        {ship.destination && ship.progress > 0 && (
          <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
            <div className="w-6 h-1 bg-gray-300 rounded-full overflow-hidden">
              <div
                className="h-full bg-blue-500 transition-all duration-1000"
                style={{ width: `${ship.progress * 100}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Ship info tooltip */}
      <div className="absolute top-10 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
        <div className="font-semibold">{ship.name}</div>
        <div className="text-xs opacity-75">
          Cargo: {cargoCount}/{ship.capacity} ({cargoPercentage.toFixed(0)}%)
        </div>
        {ship.destination && (
          <div className="text-xs opacity-75 flex items-center gap-1">
            <Clock className="w-3 h-3" />
            {(ship.progress * 100).toFixed(0)}% complete
          </div>
        )}
      </div>
    </button>
  )
}
