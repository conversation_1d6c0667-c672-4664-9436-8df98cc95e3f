"use client"

import type React from "react"

import { Badge } from "@/components/ui/badge"
import {
  TrendingUp,
  TrendingDown,
  Crown,
  Home,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  Building2,
} from "lucide-react"
import { InfoTooltip } from "./info-tooltip"
import { Tooltip, TooltipProvider } from "@/components/ui/tooltip"
import type { City, Good } from "../types/game"
import { PriceTrendChart } from "./price-trend-chart"
import { analyzeIndustry, getIndustryStatusColor, getIndustryStatusText } from "../utils/industryAnalysis"

interface CityDetailsPanelProps {
  city: City
  goods: Good[]
  priceHistory: { [goodId: string]: number[] }
  consumptionBreakdown: {
    population: { [goodId: string]: number }
    specialized: { [goodId: string]: number }
    total: { [goodId: string]: number }
    wealthLevel: "poor" | "normal" | "wealthy"
  }
  industries: any[]
  showInvestmentTab?: boolean
  investmentContent?: React.ReactNode
}

export function CityDetailsPanel({
  city,
  goods,
  priceHistory,
  consumptionBreakdown,
  industries,
  showInvestmentTab = false,
  investmentContent,
}: CityDetailsPanelProps) {
  const getWealthIcon = (wealthLevel: string) => {
    switch (wealthLevel) {
      case "wealthy":
        return <Crown className="w-4 h-4 text-yellow-600" />
      case "poor":
        return <Home className="w-4 h-4 text-gray-600" />
      default:
        return <Users className="w-4 h-4 text-blue-600" />
    }
  }

  const getWealthColor = (wealthLevel: string) => {
    switch (wealthLevel) {
      case "wealthy":
        return "text-yellow-600"
      case "poor":
        return "text-gray-600"
      default:
        return "text-blue-600"
    }
  }

  const getWealthTooltip = (wealthLevel: string) => {
    switch (wealthLevel) {
      case "wealthy":
        return "This city is wealthy and consumes more luxury goods. Industries operate at higher efficiency."
      case "poor":
        return "This city is poor and focuses on essential goods. Industries may operate at reduced efficiency."
      default:
        return "This city has moderate wealth and balanced consumption patterns."
    }
  }

  const getPriceTooltip = (good: Good, item: any) => {
    const priceChange = item.price - good.basePrice
    const changePercent = ((priceChange / good.basePrice) * 100).toFixed(0)
    const trend = priceChange > 0 ? "above" : "below"

    return (
      <div className="space-y-2">
        <div className="font-semibold text-white">{good.name}</div>
        <div className="text-gray-200">
          Current Price: <span className="text-white font-semibold">${item.price}</span>
        </div>
        <div className="text-gray-200">
          Base Price: <span className="text-white">${good.basePrice}</span>
        </div>
        <div className="text-gray-200">
          Change:{" "}
          <span className={priceChange >= 0 ? "text-red-300" : "text-green-300"}>
            {changePercent}% {trend} base
          </span>
        </div>
        <div className="text-gray-200">
          Stock: <span className="text-white">{Math.round(item.quantity)} units</span>
        </div>
        <div className="text-gray-200">
          Demand:{" "}
          <span className="text-white">{item.demand > 0 ? "High" : item.demand < -10 ? "Oversupplied" : "Normal"}</span>
        </div>
        {priceChange > good.basePrice * 0.5 && (
          <div className="text-yellow-300 font-semibold">⚠️ Very expensive - good selling opportunity!</div>
        )}
        {priceChange < -good.basePrice * 0.3 && (
          <div className="text-green-300 font-semibold">💰 Cheap - good buying opportunity!</div>
        )}
      </div>
    )
  }

  // If investment content is provided, show it instead of the regular content
  if (showInvestmentTab && investmentContent) {
    return (
      <TooltipProvider>
        <div className="space-y-2">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Building2 className="w-5 h-5" />
              {city.name} - Investment Opportunities
            </h3>
            <Tooltip content={getWealthTooltip(consumptionBreakdown.wealthLevel)}>
              <div className="flex items-center gap-1 cursor-help">
                {getWealthIcon(consumptionBreakdown.wealthLevel)}
                <span className={`text-sm font-medium capitalize ${getWealthColor(consumptionBreakdown.wealthLevel)}`}>
                  {consumptionBreakdown.wealthLevel}
                </span>
              </div>
            </Tooltip>
          </div>

          <Tooltip
            content={`${city.name} has ${city.population.toLocaleString()} residents and $${city.wealth.toLocaleString()} in city wealth for building new industries.`}
          >
            <p className="text-sm text-muted-foreground mb-4 cursor-help">
              Population: {city.population.toLocaleString()} • Wealth: ${city.wealth.toLocaleString()}
            </p>
          </Tooltip>

          {investmentContent}
        </div>
      </TooltipProvider>
    )
  }

  const marketContent = (
    <div className="space-y-2 max-h-64 overflow-y-auto">
      {Object.entries(city.inventory).map(([goodId, item]) => {
        const good = goods.find((g) => g.id === goodId)
        if (!good) return null

        const priceChange = item.price - good.basePrice
        const isExpensive = priceChange > 0
        const cityPriceHistory = priceHistory[goodId] || []

        return (
          <Tooltip key={goodId} content={getPriceTooltip(good, item)} side="left">
            <div className="flex items-center justify-between text-sm p-2 bg-gray-50 rounded hover:bg-gray-100 cursor-help transition-colors w-full">
              <div className="flex items-center gap-2">
                <span className="font-medium">{good.name}</span>
                <Badge variant="outline" className="text-xs">
                  {Math.round(item.quantity)}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <PriceTrendChart prices={cityPriceHistory} basePrice={good.basePrice} />
                <span
                  className={`flex items-center gap-1 font-semibold ${isExpensive ? "text-red-600" : "text-green-600"}`}
                >
                  {isExpensive ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}${item.price}
                </span>
              </div>
            </div>
          </Tooltip>
        )
      })}
    </div>
  )

  const economyContent = (
    <div className="space-y-4">
      <div>
        <div className="flex items-center gap-2 mb-2">
          <h4 className="font-semibold text-green-600">Production</h4>
          <InfoTooltip
            content="Raw production from the city's natural resources and basic industries. This will be gradually replaced by the new industry system."
            type="info"
          />
        </div>
        <div className="text-sm space-y-1">
          {Object.entries(city.production).map(([goodId, amount]) => {
            const good = goods.find((g) => g.id === goodId)
            return good ? (
              <Tooltip
                key={goodId}
                content={`${good.name} production: +${amount} units per tick from city's natural resources`}
              >
                <div className="flex justify-between bg-green-50 p-1 rounded hover:bg-green-100 cursor-help transition-colors">
                  <span>{good.name}</span>
                  <span className="font-semibold">+{amount}/tick</span>
                </div>
              </Tooltip>
            ) : null
          })}
          {Object.keys(city.production).length === 0 && (
            <div className="text-muted-foreground text-center py-2 bg-green-50 rounded">No raw production</div>
          )}
        </div>
      </div>

      <div>
        <div className="flex items-center gap-2 mb-2">
          <h4 className="font-semibold text-red-600">Population Consumption</h4>
          <InfoTooltip
            content={`Population-based consumption varies by city wealth. ${city.name} is ${consumptionBreakdown.wealthLevel} and has ${city.population.toLocaleString()} people.`}
            type="info"
          />
        </div>
        <div className="text-sm space-y-1">
          {Object.entries(consumptionBreakdown.population).map(([goodId, amount]) => {
            const good = goods.find((g) => g.id === goodId)
            return good ? (
              <Tooltip
                key={goodId}
                content={`${good.name} consumption: -${amount.toFixed(1)} units per tick by ${city.population.toLocaleString()} citizens`}
              >
                <div className="flex justify-between bg-red-50 p-1 rounded hover:bg-red-100 cursor-help transition-colors">
                  <span>{good.name}</span>
                  <span className="font-semibold">-{amount.toFixed(1)}/tick</span>
                </div>
              </Tooltip>
            ) : null
          })}
        </div>
      </div>

      {Object.keys(consumptionBreakdown.specialized).length > 0 && (
        <div>
          <div className="flex items-center gap-2 mb-2">
            <h4 className="font-semibold text-orange-600">Specialized Consumption</h4>
            <InfoTooltip
              content="Additional consumption for city infrastructure, construction, and specialized activities beyond basic population needs."
              type="info"
            />
          </div>
          <div className="text-sm space-y-1">
            {Object.entries(consumptionBreakdown.specialized).map(([goodId, amount]) => {
              const good = goods.find((g) => g.id === goodId)
              return good ? (
                <Tooltip
                  key={goodId}
                  content={`${good.name} specialized use: -${amount.toFixed(1)} units per tick for city infrastructure and development`}
                >
                  <div className="flex justify-between bg-orange-50 p-1 rounded hover:bg-orange-100 cursor-help transition-colors">
                    <span>{good.name}</span>
                    <span className="font-semibold">-{amount.toFixed(1)}/tick</span>
                  </div>
                </Tooltip>
              ) : null
            })}
          </div>
        </div>
      )}
    </div>
  )

  const industriesContent = (
    <div className="text-sm space-y-2">
      {industries.map((industry) => {
        const analysis = analyzeIndustry(industry, city, goods)
        const statusColor = getIndustryStatusColor(analysis.status)
        const statusText = getIndustryStatusText(analysis.status)

        const getStatusIcon = () => {
          switch (analysis.status) {
            case "active":
              return <CheckCircle className="w-3 h-3 text-green-600" />
            case "under_construction":
              return <Clock className="w-3 h-3 text-yellow-600" />
            case "inactive":
              return <AlertTriangle className="w-3 h-3 text-red-600" />
          }
        }

        const industryTooltip = (
          <div className="space-y-3">
            <div>
              <div className="font-semibold text-white text-sm">{industry.name}</div>
              <div className="text-gray-300 text-xs">{industry.recipe?.description || "Industry details"}</div>
              {industry.isPlayerOwned && (
                <div className="text-blue-300 text-xs mt-1">
                  ⭐ Player Investment: ${industry.playerInvestment.toLocaleString()} •{" "}
                  {(industry.playerProfitShare * 100).toFixed(0)}% profit share
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-3 text-xs">
              <div>
                <span className="text-gray-300">Status:</span>
                <div className="text-white font-semibold">{statusText}</div>
              </div>
              <div>
                <span className="text-gray-300">Efficiency:</span>
                <div className="text-white font-semibold">{(industry.efficiency * 100).toFixed(0)}%</div>
              </div>
            </div>

            {analysis.reasons.length > 0 && (
              <div>
                <div className="font-semibold text-red-300 text-xs mb-1">Issues:</div>
                <ul className="space-y-1">
                  {analysis.reasons.map((reason, idx) => (
                    <li key={idx} className="text-xs text-gray-200 leading-tight">
                      • {reason}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {analysis.suggestions.length > 0 && (
              <div>
                <div className="font-semibold text-blue-300 text-xs mb-1">Suggestions:</div>
                <ul className="space-y-1">
                  {analysis.suggestions.map((suggestion, idx) => (
                    <li key={idx} className="text-xs text-gray-200 leading-tight">
                      • {suggestion}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {Object.keys(analysis.inputStatus).length > 0 && (
              <div>
                <div className="font-semibold text-white text-xs mb-1">Input Status:</div>
                <div className="grid grid-cols-1 gap-1">
                  {Object.entries(analysis.inputStatus).map(([goodId, status]) => {
                    const good = goods.find((g) => g.id === goodId)
                    return (
                      <div key={goodId} className="flex justify-between text-xs">
                        <span className="text-gray-300">{good?.name}:</span>
                        <span className={status.sufficient ? "text-green-300" : "text-red-300"}>
                          {status.available}/{status.required}
                        </span>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </div>
        )

        return (
          <Tooltip key={industry.id} content={industryTooltip} side="left" fixedWidth={true}>
            <div
              className={`p-2 rounded hover:bg-purple-100 cursor-help transition-colors ${
                industry.isPlayerOwned ? "bg-blue-50 border border-blue-200" : "bg-purple-50"
              }`}
            >
              <div className="flex justify-between items-center mb-1">
                <div className="flex items-center gap-1">
                  <span className="font-medium">{industry.name}</span>
                  {industry.isPlayerOwned && (
                    <Badge variant="default" className="text-xs bg-blue-600">
                      Player
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-1">
                  <Badge variant="outline" className={`text-xs ${statusColor}`}>
                    <div className="flex items-center gap-1">
                      {getStatusIcon()}
                      {statusText}
                    </div>
                  </Badge>
                </div>
              </div>
              {industry.recipe && (
                <div className="text-xs text-purple-600 space-y-1">
                  <div className="flex justify-between">
                    <span>Efficiency: {(industry.efficiency * 100).toFixed(0)}%</span>
                    <span>Workers: {industry.recipe.workerRequirement}</span>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <span className="font-semibold">Inputs:</span>
                      {Object.entries(industry.recipe.inputs).map(([goodId, amount]) => {
                        const good = goods.find((g) => g.id === goodId)
                        const inputStatus = analysis.inputStatus[goodId]
                        return good ? (
                          <div key={goodId} className="ml-2 flex items-center gap-1">
                            <span>
                              {good.name}: {amount}/tick
                            </span>
                            {inputStatus && !inputStatus.sufficient && (
                              <AlertTriangle className="w-2 h-2 text-red-500" />
                            )}
                          </div>
                        ) : null
                      })}
                    </div>
                    <div>
                      <span className="font-semibold">Outputs:</span>
                      {Object.entries(industry.recipe.outputs).map(([goodId, amount]) => {
                        const good = goods.find((g) => g.id === goodId)
                        return good ? (
                          <div key={goodId} className="ml-2">
                            {good.name}: {amount}/tick
                          </div>
                        ) : null
                      })}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </Tooltip>
        )
      })}
      {industries.length === 0 && (
        <Tooltip content="This city hasn't built any industries yet. Industries will develop automatically based on city population, wealth, and available resources.">
          <p className="text-muted-foreground text-center py-2 bg-purple-50 rounded cursor-help">
            No industries built yet
          </p>
        </Tooltip>
      )}
    </div>
  )

  return (
    <TooltipProvider>
      <div className="space-y-2">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold">{city.name}</h3>
          <Tooltip content={getWealthTooltip(consumptionBreakdown.wealthLevel)}>
            <div className="flex items-center gap-1 cursor-help">
              {getWealthIcon(consumptionBreakdown.wealthLevel)}
              <span className={`text-sm font-medium capitalize ${getWealthColor(consumptionBreakdown.wealthLevel)}`}>
                {consumptionBreakdown.wealthLevel}
              </span>
            </div>
          </Tooltip>
        </div>

        <Tooltip
          content={`${city.name} has ${city.population.toLocaleString()} residents and $${city.wealth.toLocaleString()} in city wealth for building new industries.`}
        >
          <p className="text-sm text-muted-foreground mb-4 cursor-help">
            Population: {city.population.toLocaleString()} • Wealth: ${city.wealth.toLocaleString()}
          </p>
        </Tooltip>

        <div className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Market Prices</h4>
            {marketContent}
          </div>
          <div>
            <h4 className="font-semibold mb-2">Economy</h4>
            {economyContent}
          </div>
          <div>
            <div className="flex items-center gap-2 mb-2">
              <h4 className="font-semibold">Industries</h4>
              <InfoTooltip
                content="Industries transform raw materials into finished goods. They require inputs, workers, and time to produce outputs. Hover over each industry for detailed status information."
                type="help"
              />
            </div>
            {industriesContent}
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
