import type { City, Ship, NPCFleet } from "../types/game"

interface TradeRouteVisualizerProps {
  cities: City[]
  ships: Ship[]
  npcFleets: NPCFleet[]
  mapWidth: number
  mapHeight: number
}

export function TradeRouteVisualizer({ cities, ships, npcFleets, mapWidth, mapHeight }: TradeRouteVisualizerProps) {
  const allShips = [
    ...ships.map((ship) => ({ ...ship, isNPC: false })),
    ...npcFleets.flatMap((fleet) => fleet.ships.map((ship) => ({ ...ship, isNPC: true }))),
  ]

  return (
    <svg className="absolute inset-0 pointer-events-none" width={mapWidth} height={mapHeight} style={{ zIndex: 1 }}>
      <defs>
        {/* Gradient for player routes */}
        <linearGradient id="playerRoute" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#10b981" stopOpacity="0.8" />
          <stop offset="100%" stopColor="#10b981" stopOpacity="0.2" />
        </linearGradient>

        {/* Gradient for NPC routes */}
        <linearGradient id="npcRoute" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#f97316" stopOpacity="0.8" />
          <stop offset="100%" stopColor="#f97316" stopOpacity="0.2" />
        </linearGradient>

        {/* Arrow markers */}
        <marker id="playerArrow" viewBox="0 0 10 10" refX="9" refY="3" markerWidth="6" markerHeight="6" orient="auto">
          <path d="M0,0 L0,6 L9,3 z" fill="#10b981" />
        </marker>

        <marker id="npcArrow" viewBox="0 0 10 10" refX="9" refY="3" markerWidth="6" markerHeight="6" orient="auto">
          <path d="M0,0 L0,6 L9,3 z" fill="#f97316" />
        </marker>
      </defs>

      {/* Draw trade routes */}
      {allShips
        .filter((ship) => ship.destination && ship.currentCity)
        .map((ship) => {
          const currentCity = cities.find((c) => c.id === ship.currentCity)
          const destinationCity = cities.find((c) => c.id === ship.destination)

          if (!currentCity || !destinationCity) return null

          const strokeUrl = ship.isNPC ? "url(#npcRoute)" : "url(#playerRoute)"
          const markerUrl = ship.isNPC ? "url(#npcArrow)" : "url(#playerArrow)"

          return (
            <g key={ship.id}>
              {/* Route line */}
              <line
                x1={currentCity.position.x}
                y1={currentCity.position.y}
                x2={destinationCity.position.x}
                y2={destinationCity.position.y}
                stroke={strokeUrl}
                strokeWidth="3"
                strokeDasharray="5,5"
                markerEnd={markerUrl}
              />

              {/* Progress indicator */}
              <circle
                cx={currentCity.position.x + (destinationCity.position.x - currentCity.position.x) * ship.progress}
                cy={currentCity.position.y + (destinationCity.position.y - currentCity.position.y) * ship.progress}
                r="4"
                fill={ship.isNPC ? "#f97316" : "#10b981"}
                opacity="0.8"
              >
                <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite" />
              </circle>
            </g>
          )
        })}
    </svg>
  )
}
