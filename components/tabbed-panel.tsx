"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface Tab {
  id: string
  label: string
  icon?: React.ReactNode
  badge?: string | number
  content: React.ReactNode
}

interface TabbedPanelProps {
  title: string
  titleIcon?: React.ReactNode
  tabs: Tab[]
  defaultTab?: string
}

export function TabbedPanel({ title, titleIcon, tabs, defaultTab }: TabbedPanelProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id)

  const activeTabContent = tabs.find((tab) => tab.id === activeTab)?.content

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          {titleIcon}
          {title}
        </CardTitle>
        <div className="flex flex-wrap gap-1 mt-2">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? "default" : "outline"}
              size="sm"
              onClick={() => setActiveTab(tab.id)}
              className="flex items-center gap-2 text-xs"
            >
              {tab.icon}
              {tab.label}
              {tab.badge && (
                <Badge variant="secondary" className="text-xs ml-1">
                  {tab.badge}
                </Badge>
              )}
            </Button>
          ))}
        </div>
      </CardHeader>
      <CardContent className="pt-2">{activeTabContent}</CardContent>
    </Card>
  )
}
