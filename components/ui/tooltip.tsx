"use client"

import type * as React from "react"
import { useState } from "react"

interface TooltipProps {
  children: React.ReactNode
  content: React.ReactNode
  side?: "top" | "right" | "bottom" | "left"
  className?: string
  fixedWidth?: boolean
}

export function Tooltip({ children, content, side = "top", className = "", fixedWidth = false }: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false)

  const getSideClasses = () => {
    switch (side) {
      case "top":
        return "bottom-full left-1/2 transform -translate-x-1/2 mb-2"
      case "right":
        return "left-full top-1/2 transform -translate-y-1/2 ml-2"
      case "bottom":
        return "top-full left-1/2 transform -translate-x-1/2 mt-2"
      case "left":
        return "right-full top-1/2 transform -translate-y-1/2 mr-2"
      default:
        return "bottom-full left-1/2 transform -translate-x-1/2 mb-2"
    }
  }

  const getArrowClasses = () => {
    switch (side) {
      case "top":
        return "top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-gray-900"
      case "right":
        return "right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-gray-900"
      case "bottom":
        return "bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-gray-900"
      case "left":
        return "left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-gray-900"
      default:
        return "top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-gray-900"
    }
  }

  const getWidthClass = () => {
    if (fixedWidth) {
      return "w-80" // Fixed width for industry tooltips
    }
    return "max-w-xs" // Flexible width for other tooltips
  }

  const handleMouseEnter = () => {
    console.log("Tooltip mouse enter - showing tooltip")
    setIsVisible(true)
  }

  const handleMouseLeave = () => {
    console.log("Tooltip mouse leave - hiding tooltip")
    setIsVisible(false)
  }

  return (
    <div className="relative inline-block" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      {children}
      {isVisible && (
        <div className={`absolute z-50 ${getSideClasses()} ${className}`} style={{ pointerEvents: "none" }}>
          <div className={`bg-gray-900 text-white text-xs rounded px-3 py-2 shadow-lg ${getWidthClass()}`}>
            {content}
          </div>
          <div className={`absolute w-0 h-0 border-4 ${getArrowClasses()}`} style={{ borderWidth: "4px" }} />
        </div>
      )}
    </div>
  )
}

// For compatibility, we'll also export these
export const TooltipProvider = ({ children }: { children: React.ReactNode }) => <>{children}</>
export const TooltipTrigger = ({ children }: { children: React.ReactNode }) => <>{children}</>
export const TooltipContent = ({ children }: { children: React.ReactNode }) => <>{children}</>
