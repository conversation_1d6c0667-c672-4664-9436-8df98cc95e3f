"use client"

import type React from "react"
import { HelpCircle, AlertCircle, Info, CheckCircle, XCircle } from "lucide-react"
import { Tooltip, TooltipProvider } from "@/components/ui/tooltip"

interface InfoTooltipProps {
  content: React.ReactNode
  type?: "info" | "warning" | "error" | "success" | "help"
  side?: "top" | "right" | "bottom" | "left"
  children?: React.ReactNode
}

export function InfoTooltip({ content, type = "info", side = "top", children }: InfoTooltipProps) {
  const getIcon = () => {
    switch (type) {
      case "warning":
        return <AlertCircle className="w-3 h-3 text-yellow-600" />
      case "error":
        return <XCircle className="w-3 h-3 text-red-600" />
      case "success":
        return <CheckCircle className="w-3 h-3 text-green-600" />
      case "help":
        return <HelpCircle className="w-3 h-3 text-blue-600" />
      default:
        return <Info className="w-3 h-3 text-blue-600" />
    }
  }

  return (
    <TooltipProvider>
      <Tooltip content={content} side={side}>
        {children || (
          <button
            type="button"
            className="inline-flex items-center justify-center hover:opacity-70 transition-opacity cursor-help"
          >
            {getIcon()}
          </button>
        )}
      </Tooltip>
    </TooltipProvider>
  )
}
