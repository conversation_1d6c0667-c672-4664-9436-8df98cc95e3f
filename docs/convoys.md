## 🚢 LOGISTICS & CONVOYS — DESIGN SYSTEM OVERVIEW

---

### 🧱 **Logistics Core Concepts**

1. **Goods need to move between cities to make profit.**
2. Movement costs time, protection, and money.
3. Convoys are exposed to real threats: pirates, storms, sabotage.
4. Players can specialize: fast smugglers, tanky haulers, stealth traders.

---

## 🛳️ 1. CONVOY SETUP

### Convoy =

`Fleet + Goods + Route + Escort + Orders`

Each convoy you create is a **semi-autonomous agent**. You set it up like:

> “Move from Alexandria to Venice carrying 1000 grain. Avoid pirate zones. Stop at Crete for refuel. If intercepted: flee. On arrival: sell all at market price.”

---

### 🧭 Convoy Parameters

| Element                 | Meaning                                                            |
| ----------------------- | ------------------------------------------------------------------ |
| **Fleet Type**          | Fast sloop? Big galleon? Balanced cog?                             |
| **Cargo Type**          | Heavy perishable (fish)? Light, high-value (silk)? Refined goods?  |
| **Route Logic**         | Fastest? Safest? Trade hub path?                                   |
| **Defenses**            | Escorts (warships), bribes (for pirates), stealth (camouflage)     |
| **Rules of Engagement** | Run? Fight? Pay off attacker? Send distress signal?                |
| **Insurance**           | Pay to cover losses if ambushed? (player-run guild or AI offering) |

---

## 💥 2. RISK-REWARD BALANCING

### 💎 High Value = High Risk

* Certain routes are **hot zones** with:

  * Pirate AI
  * Rogue player fleets
  * Dangerous weather
  * Enemy city embargoes

> Example: Transporting **silk** through pirate-infested Aegean Sea could earn you 3× profit… if you survive.

---

### 🔐 Risk Mitigation Options

| Strategy                   | Trade-Off                                            |
| -------------------------- | ---------------------------------------------------- |
| **Heavy Escorts**          | Protection = slower speed + higher maintenance       |
| **Smaller Convoys**        | Lower profit, lower visibility                       |
| **Night Travel**           | Avoid detection, but weather risk increases          |
| **Convoy Diversion**       | Send decoys or multiple convoys to confuse attackers |
| **Insider Intel**          | Spy on pirate movements via bribes or agents         |
| **Guild Escort Contracts** | Hire another player to escort you for a fee          |

---

## 📦 3. CARGO CAPACITY & SPACE MANAGEMENT

### Every ship has:

* **Max Volume** (e.g. 10,000 units)
* **Weight Limits**
* **Special Storage Types** (e.g. cold storage, vaults for gold)
* You need to **optimize fleet composition** based on cargo.

> Example: You’re sending olive oil? Needs sealed barrels. Wine? Spoils in heat. Weapons? Can explode if fire breaks.

---

## 🧠 4. STRATEGIC DEPTH HOOKS

* **Trade Routes Aren’t Static** → Prices shift, pirates adapt.
* **Smuggling & Black Markets** → You can take banned goods through secret ports.
* **Fleet Upgrades** → Faster sails, stronger hulls, stealth tech, hidden compartments.
* **Convoy Naming & Logs** → Players build legendary convoys, with reputations.
* **Player Sabotage** → You can bribe enemy captains or fake distress signals.

---

## 🔄 BONUS: AUTOMATED TRADE ROUTES (Late Game)

Once trusted NPCs or family members reach high enough skill, you can assign:

> "Auto-run the Alexandria → Rome olive oil route until ROI < 30% or piracy risk > 50%."

Now your **dynasty scales**, freeing your attention for:

* **Politics**
* **City building**
* **Espionage**
* **Military expansion**

---

## 🧠 Summary: Risk vs Reward Layered Loop

| Convoy Size | Risk   | Speed  | Profit Potential | Suggested Use                    |
| ----------- | ------ | ------ | ---------------- | -------------------------------- |
| Small       | Low    | High   | Low              | Early game, stealthy, agile      |
| Medium      | Medium | Medium | Medium           | Balanced trade                   |
| Large       | High   | Low    | Very High        | End-game dominance / high-stakes |
