## 🧮 1. **Supply & Demand Pricing Formula**

### 🎯 Goal:

* Prices must fluctuate based on **actual player actions**.
* Shortages → higher prices (incentivize production/trade).
* Oversupply → lower prices (discourage hoarding, incentivize transport or refinement).

---

### 📈 Core Formula (per city, per good):

```ts
price = basePrice * (demandFactor / supplyFactor) ^ elasticity
```

#### Where:

* `basePrice`: standard market value of the good
* `demandFactor`: composite score based on:

  * Local consumption rate
  * Current population/military/city needs
  * Refining facility demand
* `supplyFactor`: current city stockpile + incoming shipments
* `elasticity`: a per-good multiplier (0.5–2.0) that controls price sensitivity

---

### 🔄 Example:

* **Grain in Carthage**

  * basePrice: 10
  * demandFactor: 100 (big city + army + bakery)
  * supplyFactor: 25 (low local stockpile)
  * elasticity: 1.2 (grain is volatile)

```ts
price = 10 * (100 / 25) ^ 1.2 = 10 * 4.0 ^ 1.2 ≈ 10 * 6.9 = 69 coins/unit
```

Now let's say a big convoy arrives and stock jumps to 300:

```ts
price = 10 * (100 / 300) ^ 1.2 = 10 * (0.33) ^ 1.2 ≈ 10 * 0.27 = 2.7 coins/unit
```

→ **Instant price crash**. Players lose money if they flood a city too hard — they need to spread and optimize.

---

## 🧊 2. **Decay, Storage & Spoilage Mechanics**

To fight hoarding and keep trade moving.

### 🧂 Goods Decay by Type:

| Type          | Decay Rate        | Storage Needed            |
| ------------- | ----------------- | ------------------------- |
| Perishables   | High (daily)      | Refrigerated / cooled     |
| Basic Goods   | Moderate (weekly) | Covered storage           |
| Durable Goods | Slow (monthly)    | Dry / guarded warehouse   |
| Luxury        | None (if stored)  | Vaults / special handling |

---

### 🏚️ Storage Mechanics:

* Each **city tile** has a limited storage capacity (e.g., 1,000 units).
* **Warehouse ownership** is capped or taxed by local lords.
* **Overflowing stock = forced decay**, looting, or sell-off at loss.
* Rich players must **invest in large, vulnerable warehouses**, which can be:

  * Burned
  * Raided
  * Seized in rebellion

---

### 💸 Storage Cost Formula:

```ts
dailyCost = baseRate * volumeStored * (rarityFactor + perishabilityFactor)
```

* **baseRate**: base cost per unit
* **rarityFactor**: bonus for rare/luxury goods
* **perishabilityFactor**: perishables need special handling
* Multiplies fast — 10,000 grain units = real cost

→ **Incentive to move goods** or **convert them to refined forms** (flour, bread, rum, cloth) that are longer-lasting or higher-margin.
